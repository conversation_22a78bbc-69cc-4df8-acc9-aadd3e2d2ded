我来为你整理这些接口的接口文档。让我先查看一下相关的代码结构，然后生成完整的接口文档。

Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/PersonalTrainingController.java现在让我查看相关的DTO类结构：

Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/order/dto/PersonalTrainingCollectionReqDTO.java Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/order/dto/PersonalTrainingApprovalReqDTO.java现在我来整理完整的接口文档：

# 个人培训与认证订单管理接口文档

## 基础信息

- **接口前缀**: `/publicbiz/order-center/personal-training`
- **请求方式**: POST/GET
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 1. 收款确认接口

### 接口信息

- **接口路径**: `/confirm-collection`
- **请求方式**: POST
- **接口描述**: 确认个人培训订单收款

### 请求参数

```json
{
  "orderId": 123,
  "orderNo": "PT1234567890",
  "collectionAmount": 5000.0,
  "collectionMethod": "cash",
  "collectionDate": "2024-12-01",
  "operatorName": "张三",
  "collectionRemark": "现金收款",
  "transactionId": "TX123456789",
  "bankAccount": "6222021234567890123",
  "bankName": "中国工商银行"
}
```

### 参数说明

| 参数名           | 类型       | 必填 | 说明                  | 示例值                |
| ---------------- | ---------- | ---- | --------------------- | --------------------- |
| orderId          | Long       | 是   | 订单ID                | 123                   |
| orderNo          | String     | 是   | 订单号                | "PT1234567890"        |
| collectionAmount | BigDecimal | 是   | 收款金额（必须大于0） | 5000.00               |
| collectionMethod | String     | 是   | 收款方式              | "cash"                |
| collectionDate   | LocalDate  | 是   | 收款日期              | "2024-12-01"          |
| operatorName     | String     | 是   | 操作人姓名            | "张三"                |
| collectionRemark | String     | 否   | 收款备注              | "现金收款"            |
| transactionId    | String     | 否   | 第三方交易号          | "TX123456789"         |
| bankAccount      | String     | 否   | 银行账户信息          | "6222021234567890123" |
| bankName         | String     | 否   | 银行名称              | "中国工商银行"        |

### 响应结果

```json
{
  "code": 0,
  "data": {
    "id": 456,
    "orderId": 123,
    "orderNo": "PT1234567890",
    "collectionAmount": 5000.0,
    "collectionMethod": "cash",
    "collectionDate": "2024-12-01",
    "operatorName": "张三",
    "collectionStatus": "confirmed"
  },
  "msg": "成功"
}
```

---

## 2. 收款更新接口

### 接口信息

- **接口路径**: `/update-collection`
- **请求方式**: POST
- **接口描述**: 更新个人培训订单收款信息

### 请求参数

```json
{
  "orderId": 123,
  "orderNo": "PT1234567890",
  "collectionAmount": 6000.0,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-12-02",
  "operatorName": "李四",
  "collectionRemark": "银行转账收款",
  "transactionId": "TX123456790",
  "bankAccount": "6222021234567890124",
  "bankName": "中国建设银行"
}
```

### 参数说明

同收款确认接口

### 响应结果

```json
{
  "code": 0,
  "data": {
    "id": 456,
    "orderId": 123,
    "orderNo": "PT1234567890",
    "collectionAmount": 6000.0,
    "collectionMethod": "bank_transfer",
    "collectionDate": "2024-12-02",
    "operatorName": "李四",
    "collectionStatus": "updated"
  },
  "msg": "成功"
}
```

---

## 3. 发起审批接口

### 接口信息

- **接口路径**: `/submit-approval`
- **请求方式**: POST
- **接口描述**: 提交个人培训订单审批申请

### 请求参数

```json
{
  "orderId": 123,
  "approvalType": "training_approval",
  "approvalLevel": 1,
  "approverIds": [456, 789],
  "approvalOpinion": "培训计划合理，申请审批",
  "nextApproverIds": [101, 102],
  "remark": "个人培训订单审批",
  "operatorName": "王五"
}
```

### 参数说明

| 参数名          | 类型       | 必填 | 说明               | 示例值                   |
| --------------- | ---------- | ---- | ------------------ | ------------------------ |
| orderId         | Long       | 是   | 订单ID             | 123                      |
| approvalId      | Long       | 否   | 审批ID             | 456                      |
| approvalType    | String     | 是   | 审批类型           | "training_approval"      |
| approvalLevel   | Integer    | 是   | 审批级别           | 1                        |
| approverIds     | List<Long> | 是   | 审批人ID列表       | [456, 789]               |
| approvalResult  | String     | 否   | 审批结果           | "pending"                |
| approvalOpinion | String     | 否   | 审批意见           | "培训计划合理，申请审批" |
| rejectReason    | String     | 否   | 拒绝原因           | -                        |
| nextApproverIds | List<Long> | 否   | 下一级审批人ID列表 | [101, 102]               |
| remark          | String     | 否   | 备注信息           | "个人培训订单审批"       |
| operatorName    | String     | 否   | 操作人姓名         | "王五"                   |

### 响应结果

```json
{
  "code": 0,
  "data": {
    "id": 789,
    "orderId": 123,
    "orderNo": "PT1234567890",
    "approvalType": "training_approval",
    "approvalLevel": 1,
    "approvalStatus": "pending",
    "approverIds": [456, 789],
    "approvalOpinion": "培训计划合理，申请审批",
    "createTime": "2024-12-01T10:00:00"
  },
  "msg": "成功"
}
```

---

## 4. 审批通过接口

### 接口信息

- **接口路径**: `/approve`
- **请求方式**: POST
- **接口描述**: 审批通过个人培训订单

### 请求参数

```json
{
  "orderId": 123,
  "approvalType": "training_approval",
  "approvalLevel": 1,
  "approverIds": [456],
  "approvalResult": "approved",
  "approvalOpinion": "培训内容符合要求，审批通过",
  "operatorName": "审批员"
}
```

### 参数说明

| 参数名          | 类型       | 必填 | 说明         | 示例值                       |
| --------------- | ---------- | ---- | ------------ | ---------------------------- |
| orderId         | Long       | 是   | 订单ID       | 123                          |
| approvalType    | String     | 是   | 审批类型     | "training_approval"          |
| approvalLevel   | Integer    | 是   | 审批级别     | 1                            |
| approverIds     | List<Long> | 是   | 审批人ID列表 | [456]                        |
| approvalResult  | String     | 是   | 审批结果     | "approved"                   |
| approvalOpinion | String     | 否   | 审批意见     | "培训内容符合要求，审批通过" |
| operatorName    | String     | 否   | 操作人姓名   | "审批员"                     |

### 响应结果

```json
{
  "code": 0,
  "data": true,
  "msg": "成功"
}
```

---

## 5. 审批拒绝接口

### 接口信息

- **接口路径**: `/reject`
- **请求方式**: POST
- **接口描述**: 审批拒绝个人培训订单

### 请求参数

```json
{
  "orderId": 123,
  "approvalType": "training_approval",
  "approvalLevel": 1,
  "approverIds": [456],
  "approvalResult": "rejected",
  "approvalOpinion": "培训计划不够详细",
  "rejectReason": "培训计划缺乏具体实施方案，预算超出范围",
  "operatorName": "审批员"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
| --- | --- | --- | --- | --- |
| orderId | Long | 是 | 订单ID | 123 |
| approvalType | String | 是 | 审批类型 | "training_approval" |
| approvalLevel | Integer | 是 | 审批级别 | 1 |
| approverIds | List<Long> | 是 | 审批人ID列表 | [456] |
| approvalResult | String | 是 | 审批结果 | "rejected" |
| approvalOpinion | String | 否 | 审批意见 | "培训计划不够详细" |
| **rejectReason** | String | **是** | **拒绝原因（拒绝时必填）** | "培训计划缺乏具体实施方案，预算超出范围" |
| operatorName | String | 否 | 操作人姓名 | "审批员" |

### 响应结果

```json
{
  "code": 0,
  "data": true,
  "msg": "成功"
}
```

---

## 通用响应格式

### 成功响应

```json
{
  "code": 0,
  "data": "响应数据",
  "msg": "成功"
}
```

### 错误响应

```json
{
  "code": 错误码,
  "data": null,
  "msg": "错误信息"
}
```

### 分页响应

```json
{
  "code": 0,
  "data": {
    "list": ["数据列表"],
    "total": 100
  },
  "msg": "成功"
}
```

---

## 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 0      | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

---

## 注意事项

1. **权限控制**: 所有接口都需要相应的权限验证
2. **数据验证**: 请求参数会进行格式和业务规则验证
3. **事务处理**: 涉及数据修改的操作都使用事务保证数据一致性
4. **日志记录**: 所有操作都会记录详细的操作日志
5. **状态管理**: 订单状态变更会严格按照业务流程进行控制

---

## 接口调用示例

### cURL示例

````bash
# 收款确认
curl -X POST "http://localhost:8080/publicbiz/order-center/personal-training/confirm-collection" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 123,
    "orderNo": "PT1234567890",
    "collectionAmount": 5000.00,
    "collectionMethod": "cash",
    "collectionDate": "2024-12-01",
    "operatorName": "张三"
  }'

# 审批通过
curl -X POST "http://localhost:8080/publicbiz/order-center/personal-training/approve" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 123,
    "approvalType": "training_approval",
    "approvalLevel": 1,
    "approverIds": [456],
    "approvalResult": "approved",
    "approvalOpinion": "审批通过",
    "operatorName": "审批员"
  }'
`

# 个人培训与认证订单管理接口文档（续）

---

## 6. 审批列表查询接口

### 接口信息
- **接口路径**: `/approval-page`
- **请求方式**: POST
- **接口描述**: 分页查询个人培训订单审批记录列表

### 请求参数

```json
{
  "orderId": 123,
  "page": 1,
  "size": 20,
  "approvalType": "training_approval",
  "approvalResult": "pending",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31"
}
````

### 参数说明

| 参数名         | 类型    | 必填 | 说明                         | 示例值              |
| -------------- | ------- | ---- | ---------------------------- | ------------------- |
| orderId        | Long    | 是   | 订单ID                       | 123                 |
| page           | Integer | 否   | 当前页码（默认1）            | 1                   |
| size           | Integer | 否   | 每页大小（默认10）           | 20                  |
| approvalType   | String  | 否   | 审批类型筛选                 | "training_approval" |
| approvalResult | String  | 否   | 审批结果筛选                 | "pending"           |
| startDate      | String  | 否   | 开始日期（格式：yyyy-MM-dd） | "2024-01-01"        |
| endDate        | String  | 否   | 结束日期（格式：yyyy-MM-dd） | "2024-12-31"        |

### 审批类型说明

| 审批类型值             | 说明     |
| ---------------------- | -------- |
| training_approval      | 培训审批 |
| certification_approval | 认证审批 |
| contract_approval      | 合同审批 |
| payment_approval       | 付款审批 |

### 审批结果说明

| 审批结果值 | 说明   |
| ---------- | ------ |
| pending    | 待审批 |
| approved   | 已通过 |
| rejected   | 已拒绝 |
| processing | 审批中 |
| completed  | 已完成 |

### 响应结果

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "approvalId": 789,
        "approvalNo": "AP20241201001",
        "orderId": 123,
        "approvalType": "training_approval",
        "approvalLevel": 1,
        "approvalResult": "pending",
        "approvalOpinion": "培训计划合理，申请审批",
        "approverName": "审批员A",
        "approvalTime": "2024-12-01T10:00:00",
        "createTime": "2024-12-01T09:00:00",
        "updateTime": "2024-12-01T09:00:00"
      },
      {
        "approvalId": 790,
        "approvalNo": "AP20241201002",
        "orderId": 124,
        "approvalType": "certification_approval",
        "approvalLevel": 2,
        "approvalResult": "approved",
        "approvalOpinion": "认证材料完整，审批通过",
        "approverName": "审批员B",
        "approvalTime": "2024-12-01T14:30:00",
        "createTime": "2024-12-01T13:00:00",
        "updateTime": "2024-12-01T14:30:00"
      }
    ],
    "total": 2
  },
  "msg": "成功"
}
```

### 响应字段说明

| 字段名          | 类型    | 说明       | 示例值                   |
| --------------- | ------- | ---------- | ------------------------ |
| approvalId      | Long    | 审批ID     | 789                      |
| approvalNo      | String  | 审批单号   | "AP20241201001"          |
| orderId         | Long    | 订单ID     | 123                      |
| approvalType    | String  | 审批类型   | "training_approval"      |
| approvalLevel   | Integer | 审批级别   | 1                        |
| approvalResult  | String  | 审批结果   | "pending"                |
| approvalOpinion | String  | 审批意见   | "培训计划合理，申请审批" |
| approverName    | String  | 审批人姓名 | "审批员A"                |
| approvalTime    | String  | 审批时间   | "2024-12-01T10:00:00"    |
| createTime      | String  | 创建时间   | "2024-12-01T09:00:00"    |
| updateTime      | String  | 更新时间   | "2024-12-01T09:00:00"    |

---

## 审批列表查询接口调用示例

### cURL示例

```bash
# 分页查询审批记录
curl -X POST "http://localhost:8080/publicbiz/order-center/personal-training/approval-page" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": 123,
    "page": 1,
    "size": 20,
    "approvalType": "training_approval",
    "approvalResult": "pending",
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  }'

# 测试接口查询审批记录
curl -X GET "http://localhost:8080/publicbiz/order-center/personal-training/approval-page-test?orderNo=PT1234567890&page=1&size=20"
```

### JavaScript示例

```javascript
// 分页查询审批记录
const getApprovalList = async (params) => {
  try {
    const response = await fetch('/publicbiz/order-center/personal-training/approval-page', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        orderId: params.orderId,
        page: params.page || 1,
        size: params.size || 20,
        approvalType: params.approvalType,
        approvalResult: params.approvalResult,
        startDate: params.startDate,
        endDate: params.endDate
      })
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('查询审批记录失败:', error)
    throw error
  }
}

// 使用示例
getApprovalList({
  orderId: 123,
  page: 1,
  size: 20,
  approvalType: 'training_approval',
  approvalResult: 'pending',
  startDate: '2024-01-01',
  endDate: '2024-12-31'
}).then((result) => {
  console.log('审批记录:', result.data)
})
```

---

## 审批列表查询功能特性

### ✅ **查询功能**

- **分页查询**: 支持分页查询，可自定义页码和每页大小
- **条件筛选**: 支持按订单ID、审批类型、审批结果等条件筛选
- **时间范围**: 支持按时间范围查询审批记录
- **排序功能**: 默认按创建时间倒序排列

### ✅ **数据展示**

- **审批状态**: 清晰显示审批结果和状态
- **审批流程**: 显示审批级别和审批人信息
- **时间信息**: 记录创建、更新、审批等关键时间点
- **审批意见**: 保存完整的审批意见和说明

### ✅ **权限控制**

- **主接口**: 需要 `publicbiz:personal-training:query` 权限
- **测试接口**: 无权限验证，方便开发和测试

### ✅ **业务场景**

- **审批管理**: 查看所有审批记录和状态
- **流程跟踪**: 跟踪订单审批流程和进度
- **统计分析**: 统计审批通过率、处理时长等指标
- **历史查询**: 查询历史审批记录和决策

---

## 注意事项

1. **分页参数**: page从1开始，size建议不超过100
2. **时间格式**: 日期参数使用 `yyyy-MM-dd` 格式
3. **筛选条件**: 所有筛选条件都是可选的，不传则不进行筛选
4. **数据权限**: 主接口需要相应权限，测试接口无需权限
5. **性能考虑**: 建议合理使用筛选条件，避免查询大量数据

现在你的接口文档已经完整了，包含了所有收款、审批相关的接口，以及新增的审批列表查询接口！

我来查看现有控制器中的合同管理接口，并生成对应的接口文档内容。让我先查看PersonalTrainingController中是否已有合同管理相关的接口：

Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/PersonalTrainingController.java很好！我看到PersonalTrainingController中已经有合同管理相关的接口了。现在让我查看相关的DTO和VO类，然后生成完整的接口文档内容：

Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/order/dto/PersonalTrainingContractReqDTO.java Read file: hr-service-platform/bztmaster-module-publicbiz/bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/order/dto/PersonalTrainingContractRespDTO.java基于现有控制器中的合同管理接口，我来生成对应的接口文档内容：

## 📋 **个人培训订单合同管理接口文档**

### **1. 确认合同接口**

#### **接口信息**

- **接口路径**: `POST /admin-api/publicbiz/order-center/personal-training/confirm-contract`
- **接口名称**: 确认合同
- **接口描述**: 确认个人培训订单的合同信息
- **权限要求**: `publicbiz:personal-training:contract`

#### **请求参数**

```json
{
  "orderId": 1,
  "contractType": "electronic",
  "contractFileUrl": "https://example.com/contract.pdf",
  "contractStatus": "signed",
  "signer": "王小明",
  "signDate": "2024-06-20",
  "remark": "合同确认"
}
```

#### **参数说明**

| 参数名          | 类型   | 必填 | 说明        | 示例值                           |
| --------------- | ------ | ---- | ----------- | -------------------------------- |
| orderId         | Long   | 是   | 订单ID      | 1                                |
| contractType    | String | 是   | 合同类型    | electronic/paper                 |
| contractFileUrl | String | 否   | 合同文件URL | https://example.com/contract.pdf |
| contractStatus  | String | 是   | 合同状态    | signed                           |
| signer          | String | 否   | 签约人      | 王小明                           |
| signDate        | String | 否   | 签约日期    | 2024-06-20                       |
| remark          | String | 否   | 备注信息    | 合同确认                         |

#### **响应结果**

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

---

### **2. 更新合同信息接口**

#### **接口信息**

- **接口路径**: `POST /admin-api/publicbiz/order-center/personal-training/update-contract`
- **接口名称**: 更新合同信息
- **接口描述**: 更新个人培训订单的合同信息
- **权限要求**: `publicbiz:personal-training:contract`

#### **请求参数**

```json
{
  "orderId": 1,
  "contractType": "electronic",
  "contractFileUrl": "https://example.com/contract_updated.pdf",
  "contractStatus": "archived",
  "signer": "王小明",
  "signDate": "2024-06-21",
  "remark": "合同已归档"
}
```

#### **参数说明**

| 参数名          | 类型   | 必填 | 说明        | 示例值                                   |
| --------------- | ------ | ---- | ----------- | ---------------------------------------- |
| orderId         | Long   | 是   | 订单ID      | 1                                        |
| contractType    | String | 是   | 合同类型    | electronic/paper                         |
| contractFileUrl | String | 否   | 合同文件URL | https://example.com/contract_updated.pdf |
| contractStatus  | String | 是   | 合同状态    | archived                                 |
| signer          | String | 否   | 签约人      | 王小明                                   |
| signDate        | String | 否   | 签约日期    | 2024-06-21                               |
| remark          | String | 否   | 备注信息    | 合同已归档                               |

#### **响应结果**

```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

---

### **3. 获取合同信息接口**

#### **接口信息**

- **接口路径**: `GET /admin-api/publicbiz/order-center/personal-training/get-contract-info/{orderId}`
- **接口名称**: 获取合同信息
- **接口描述**: 根据订单ID获取合同详细信息
- **权限要求**: `publicbiz:personal-training:query`

#### **请求参数**

| 参数名  | 类型 | 必填 | 说明               | 示例值 |
| ------- | ---- | ---- | ------------------ | ------ |
| orderId | Long | 是   | 订单ID（路径参数） | 1      |

#### **响应结果**

```json
{
  "code": 0,
  "data": {
    "contractId": 1,
    "orderId": 1,
    "contractType": "electronic",
    "contractFileUrl": "https://example.com/contract.pdf",
    "contractStatus": "signed",
    "signer": "王小明",
    "signDate": "2024-06-20",
    "createTime": "2024-06-20 10:00:00",
    "updateTime": "2024-06-20 10:00:00"
  },
  "msg": "操作成功"
}
```

---

### **数据字典**

#### **合同类型 (contractType)**

- `electronic` - 电子合同
- `paper` - 纸质合同

#### **合同状态 (contractStatus)**

- `draft` - 草稿
- `pending` - 待签署
- `signed` - 已签署
- `archived` - 已归档
- `cancelled` - 已作废

#### **错误码说明**

- `0` - 操作成功
- `1001` - 参数错误
- `1002` - 合同不存在
- `1003` - 订单不存在
- `1004` - 合同状态不允许操作
- `5000` - 系统内部错误

---

### **接口调用示例**

#### **确认合同**

```bash
curl -X POST "http://localhost:8080/admin-api/publicbiz/order-center/personal-training/confirm-contract" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "orderId": 1,
    "contractType": "electronic",
    "contractFileUrl": "https://example.com/contract.pdf",
    "contractStatus": "signed",
    "signer": "王小明",
    "signDate": "2024-06-20",
    "remark": "合同确认"
  }'
```

#### **更新合同**

```bash
curl -X POST "http://localhost:8080/admin-api/publicbiz/order-center/personal-training/update-contract" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "orderId": 1,
    "contractType": "electronic",
    "contractFileUrl": "https://example.com/contract_updated.pdf",
    "contractStatus": "archived",
    "signer": "王小明",
    "signDate": "2024-06-21",
    "remark": "合同已归档"
  }'
```

#### **获取合同信息**

```bash
curl -X GET "http://localhost:8080/admin-api/publicbiz/order-center/personal-training/get-contract-info/1" \
  -H "Authorization: Bearer {token}"
```

---

### **注意事项**

1. 所有接口都需要进行权限验证
2. 合同状态变更需要记录操作日志
3. 合同文件URL需要是有效的文件链接
4. 签约日期格式建议使用 `yyyy-MM-dd` 格式
5. 合同状态变更需要符合业务规则（如已签署的合同不能改为草稿）
