<!--
  页面名称：个人培训订单新增/编辑表单
  功能描述：新增/编辑个人培训订单，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑个人培训订单' : '新增个人培训订单'"
    size="60%"
    :before-close="handleClose"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="individual-training-form"
    >
      <!-- 订单信息 -->
      <div class="section-title">订单信息</div>

      <el-form-item label="关联商机" prop="businessOpportunity">
        <el-select
          v-model="form.businessOpportunity"
          placeholder="请选择关联商机"
          style="width: 100%"
          :loading="loadingDropdown"
          @change="handleBusinessOpportunityChange"
          clearable
        >
          <el-option
            v-for="item in businessOptions"
            :key="item.id"
            :label="`${item.name} - ${item.customerName} - ${item.businessType}`"
            :value="item.id"
          />
        </el-select>
        <div class="field-hint">选择关联商机可自动填充部分课程信息</div>
        <!-- 显示选中的商机信息 -->
        <div v-if="form.businessOpportunity" class="selected-info">
          <el-tag type="success" size="small">
            {{ getSelectedBusinessInfo() }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="关联线索" prop="associatedLead">
        <el-select
          v-model="form.associatedLead"
          placeholder="请选择关联线索"
          style="width: 100%"
          :loading="loadingDropdown"
          @change="handleLeadChange"
          clearable
        >
          <el-option
            v-for="item in leadOptions"
            :key="item.id"
            :label="`${item.leadId} - ${item.customerName} ${item.customerPhone} - ${item.businessModule}`"
            :value="item.id"
          />
        </el-select>
        <div class="field-hint">选择关联线索可自动填充学员信息</div>
        <!-- 显示选中的线索信息 -->
        <div v-if="form.associatedLead" class="selected-info">
          <el-tag type="info" size="small">
            {{ getSelectedLeadInfo() }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="订单类型" prop="orderType" required>
        <el-radio-group v-model="form.orderType">
          <el-radio label="training">个人培训</el-radio>
          <el-radio label="certification">考试认证</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="学员姓名" prop="studentName" required>
        <el-input v-model="form.studentName" placeholder="请输入学员姓名" />
      </el-form-item>

      <el-form-item label="课程/考试项目" prop="courseName" required>
        <el-select v-model="form.courseName" placeholder="请选择课程/考试项目" style="width: 100%">
          <el-option label="项目管理PMP认证课程" value="pmp_course" />
          <el-option label="Python编程基础课程" value="python_course" />
          <el-option label="高级母婴护理师认证" value="maternal_care" />
          <el-option label="营养师资格认证" value="nutritionist" />
          <el-option label="心理咨询师三级认证" value="psychologist" />
          <el-option label="数据分析师认证课程" value="data_analyst" />
        </el-select>
      </el-form-item>

      <el-form-item label="订单来源" prop="orderSource" required>
        <el-select v-model="form.orderSource" placeholder="请选择订单来源" style="width: 100%">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="订单金额" prop="orderAmount" required>
        <el-input v-model="form.orderAmount" placeholder="请输入订单金额" />
      </el-form-item>

      <el-form-item label="支付状态" prop="paymentStatus" required>
        <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </el-form-item>

      <!-- 支付信息模块 -->
      <div v-if="form.paymentStatus === 'paid'" class="section-title">
        <el-icon class="section-icon"><Money /></el-icon>
        支付信息
      </div>

      <div v-if="form.paymentStatus === 'paid'">
        <el-form-item label="收款金额" prop="collectionAmount" required>
          <el-input v-model="form.collectionAmount" placeholder="请输入实际收款金额" />
        </el-form-item>

        <el-form-item label="收款方式" prop="collectionMethod" required>
          <el-select
            v-model="form.collectionMethod"
            placeholder="请选择收款方式"
            style="width: 100%"
          >
            <el-option label="银行转账" value="bank_transfer" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信支付" value="wechat_pay" />
            <el-option label="现金" value="cash" />
          </el-select>
        </el-form-item>

        <el-form-item label="收款日期" prop="collectionDate" required>
          <el-date-picker
            v-model="form.collectionDate"
            type="datetime"
            placeholder="年-月-日 --:--"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="操作人" prop="operator" required>
          <el-input v-model="form.operator" placeholder="请输入操作人姓名" />
        </el-form-item>

        <el-form-item label="收款备注" prop="collectionRemarks">
          <el-input
            v-model="form.collectionRemarks"
            type="textarea"
            :rows="3"
            placeholder="请输入收款备注信息 (可选)"
          />
        </el-form-item>
      </div>

      <el-form-item label="学习/考试状态" prop="learningStatus" required>
        <el-select
          v-model="form.learningStatus"
          placeholder="请选择学习/考试状态"
          style="width: 100%"
        >
          <el-option label="未开始" value="not_started" />
          <el-option label="学习中" value="learning" />
          <el-option label="待考试" value="pending_exam" />
          <el-option label="学习中/待考试" value="learning_pending_exam" />
          <el-option label="待确认" value="pending_confirmation" />
          <el-option label="已通过" value="passed" />
          <el-option label="已完成" value="completed" />
        </el-select>
      </el-form-item>

      <!-- 合同管理 -->
      <div class="section-title">合同管理</div>

      <el-form-item label="合同类型" prop="contractType">
        <el-radio-group v-model="form.contractType">
          <el-radio label="electronic">电子合同</el-radio>
          <el-radio label="paper">纸质合同</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 电子合同时只显示合同模板 -->
      <el-form-item
        v-if="form.contractType === 'electronic'"
        label="合同模板"
        prop="contractTemplate"
      >
        <el-select v-model="form.contractTemplate" placeholder="请选择合同模板" style="width: 100%">
          <el-option label="个人培训协议模板" value="training_contract" />
          <el-option label="考试认证协议模板" value="certification_contract" />
          <el-option label="通用协议模板" value="general_contract" />
        </el-select>
      </el-form-item>

      <!-- 纸质合同时显示完整的合同管理字段 -->
      <div v-if="form.contractType === 'paper'">
        <el-form-item label="合同附件" prop="contractAttachment">
          <!-- 编辑模式下显示现有附件和下载按钮 -->
          <div v-if="isEdit && form.contractAttachment" class="existing-attachment">
            <div class="attachment-info">
              <el-icon class="attachment-icon"><Document /></el-icon>
              <span class="attachment-name">{{ getFileName(form.contractAttachment) }}</span>
            </div>
            <div class="attachment-actions">
              <el-button
                type="primary"
                size="small"
                @click="downloadAttachment(form.contractAttachment)"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              <el-button type="danger" size="small" @click="removeAttachment">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>

          <!-- 新增模式或没有附件时显示上传组件 -->
          <el-upload
            v-if="!isEdit || !form.contractAttachment"
            class="upload-demo"
            :action="uploadUrl"
            :http-request="httpRequest"
            :auto-upload="true"
            :on-success="handleFileUploadSuccess"
            :on-error="handleFileUploadError"
            :on-change="handleFileChange"
            :file-list="fileList"
            :before-upload="beforeFileUpload"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            :limit="1"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、图片格式，文件大小不超过 10MB
                {{ fileList.length > 0 ? '已选择文件' : '未选择任何文件' }}
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="合同编号" prop="contractNumber">
          <el-input v-model="form.contractNumber" placeholder="请输入合同编号" />
        </el-form-item>

        <el-form-item label="合同名称" prop="contractName">
          <el-input v-model="form.contractName" placeholder="请输入合同名称" />
        </el-form-item>

        <el-form-item label="签署日期" prop="signingDate">
          <el-date-picker
            v-model="form.signingDate"
            type="date"
            placeholder="年-月-日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="合同金额" prop="contractAmount">
          <el-input v-model="form.contractAmount" placeholder="请输入合同金额" />
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Money, Document, Download, Delete } from '@element-plus/icons-vue'
import type { FormInstance, UploadFile } from 'element-plus'
import { IndividualTrainingOrderApi } from '@/api/OrderCenter/IndividualtrainingOrder'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import { useUpload } from '@/components/UploadFile/src/useUpload'

// Props
interface Props {
  visible: boolean
  editData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
/** 表单引用 */
const formRef = ref<FormInstance>()

/** 上传配置 - 使用 contract 目录 */
const { uploadUrl, httpRequest } = useUpload('contract')

/** 表单数据 */
const form = ref({
  id: undefined as number | undefined,
  businessOpportunity: undefined as number | undefined,
  associatedLead: undefined as number | undefined,
  orderType: 'certification',
  studentName: '',
  courseName: 'pmp_course',
  orderSource: '',
  orderAmount: '',
  paymentStatus: 'pending',
  learningStatus: 'learning_pending_exam',
  contractType: 'paper',
  contractTemplate: 'training_contract',
  collectionAmount: '',
  collectionMethod: '',
  collectionDate: '',
  operator: '',
  collectionRemarks: '',
  contractAttachment: '',
  contractNumber: '',
  contractName: '',
  signingDate: '',
  contractAmount: ''
})

/** 基础表单校验规则 */
const baseRules = {
  orderType: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  studentName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
  courseName: [{ required: true, message: '请选择课程/考试项目', trigger: 'change' }],
  orderSource: [{ required: true, message: '请选择订单来源', trigger: 'change' }],
  orderAmount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }],
  learningStatus: [{ required: true, message: '请选择学习/考试状态', trigger: 'change' }]
}

/** 动态表单校验规则 */
const rules = computed(() => {
  const dynamicRules: any = { ...baseRules }

  // 如果支付状态为已支付，则验证支付信息字段
  if (form.value.paymentStatus === 'paid') {
    dynamicRules.collectionAmount = [{ required: true, message: '请输入收款金额', trigger: 'blur' }]
    dynamicRules.collectionMethod = [
      { required: true, message: '请选择收款方式', trigger: 'change' }
    ]
    dynamicRules.collectionDate = [{ required: true, message: '请选择收款日期', trigger: 'change' }]
    dynamicRules.operator = [{ required: true, message: '请输入操作人姓名', trigger: 'blur' }]
  }

  return dynamicRules
})

/** 文件列表 */
const fileList = ref<UploadFile[]>([])

/** 加载状态 */
const loading = ref(false)

/** 是否为编辑模式 */
const isEdit = ref(false)

/** 商机选项列表 */
const businessOptions = ref<any[]>([])

/** 线索选项列表 */
const leadOptions = ref<any[]>([])

/** 来源选项列表 */
const sourceOptions = ref<Array<{ label: string; value: string }>>([])

/** 加载商机线索状态 */
const loadingDropdown = ref(false)

// 方法
/** 获取商机和线索下拉数据 */
const fetchDropdownData = async () => {
  loadingDropdown.value = true
  try {
    const result = await UniversityPracticeOrderApi.getDropdownData({
      orderType: 'certification',
      businessLine: '认证业务'
    })
    businessOptions.value = result.businessOptions || []
    leadOptions.value = result.leadOptions || []

    // 从线索数据中提取来源选项
    if (result.leadOptions && result.leadOptions.length > 0) {
      const sources = new Set<string>()
      result.leadOptions.forEach((lead) => {
        if (lead.leadSource) {
          sources.add(lead.leadSource)
        }
      })

      // 转换为选项格式
      sourceOptions.value = Array.from(sources).map((source) => ({
        label: source,
        value: source
      }))
    }

    console.log('获取到的商机数据:', result.businessOptions)
    console.log('获取到的线索数据:', result.leadOptions)
    console.log('提取的来源选项:', sourceOptions.value)

    // 如果没有数据，显示提示
    if (result.businessOptions?.length === 0) {
      console.warn('未获取到商机数据')
    }
    if (result.leadOptions?.length === 0) {
      console.warn('未获取到线索数据')
    }
  } catch (error) {
    console.error('获取商机线索数据失败:', error)
    ElMessage.error('获取商机线索数据失败')
  } finally {
    loadingDropdown.value = false
  }
}

/** 处理文件变化 */
const handleFileChange = (file: UploadFile) => {
  console.log('=== 文件变化回调触发 ===')
  console.log('选择的文件:', file)
  console.log('文件状态:', file.status)
  console.log('文件响应:', file.response)
  console.log('文件原始数据:', file.raw)

  fileList.value = [file]

  // 使用 useUpload 后，文件会自动上传，这里主要处理文件选择逻辑
  console.log('文件已选择:', file.name)

  // 如果是本地文件且还未上传，可以设置一个临时标识
  if (file.raw && !file.response) {
    form.value.contractAttachment = 'pending_upload'
    console.log('文件等待上传中...')
    console.log('当前表单contractAttachment值:', form.value.contractAttachment)
  }
}

/** 文件上传成功回调 */
const handleFileUploadSuccess = (response: any, file: UploadFile) => {
  console.log('=== 文件上传成功回调触发 ===')
  console.log('完整响应数据:', response)
  console.log('文件对象:', file)
  console.log('响应数据类型:', typeof response)
  console.log('响应数据结构:', Object.keys(response || {}))

  // 尝试多种数据格式来获取文件URL
  let fileUrl = ''

  if (response && response.data) {
    console.log('响应data字段:', response.data)
    console.log('data字段类型:', typeof response.data)
    console.log('data字段结构:', Object.keys(response.data || {}))

    // 尝试不同的URL字段名
    fileUrl =
      response.data.url ||
      response.data.fileUrl ||
      response.data.path ||
      response.data.downloadUrl ||
      ''

    if (fileUrl) {
      console.log('找到文件URL:', fileUrl)
    } else {
      console.log('未找到文件URL，尝试其他方式...')
      // 如果data本身就是URL字符串
      if (typeof response.data === 'string') {
        fileUrl = response.data
        console.log('data本身就是URL:', fileUrl)
      }
    }
  } else if (typeof response === 'string') {
    // 如果response本身就是URL字符串
    fileUrl = response
    console.log('response本身就是URL:', fileUrl)
  }

  // 保存URL到表单
  if (fileUrl) {
    form.value.contractAttachment = fileUrl
    console.log('✅ 合同附件URL已保存到表单:', fileUrl)
    console.log('当前表单contractAttachment值:', form.value.contractAttachment)
    ElMessage.success('文件上传成功')
  } else {
    console.error('❌ 无法获取文件URL，请检查响应数据格式')
    ElMessage.warning('文件上传成功，但无法获取文件地址')
  }
}

/** 文件上传失败回调 */
const handleFileUploadError = (error: any, file: UploadFile) => {
  console.error('=== 文件上传失败回调触发 ===')
  console.error('错误详情:', error)
  console.error('文件对象:', file)
  console.error('错误类型:', typeof error)
  console.error('错误结构:', Object.keys(error || {}))

  ElMessage.error('文件上传失败，请重试')

  // 从文件列表中移除失败的文件
  const index = fileList.value.findIndex((f) => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
    console.log('已从文件列表中移除失败的文件')
  }
}

/** 文件上传前的验证 */
const beforeFileUpload = (file: File) => {
  const isValidType = /\.(pdf|doc|docx|jpg|jpeg|png)$/i.test(file.name)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF、Word、图片格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

/** 处理商机选择 */
const handleBusinessOpportunityChange = (value: string) => {
  if (value) {
    const selectedBusiness = businessOptions.value.find((item) => item.id === value)
    if (selectedBusiness) {
      // 自动填充课程相关信息
      if (selectedBusiness.businessType) {
        // 根据商机类型自动选择课程
        if (
          selectedBusiness.businessType.includes('PMP') ||
          selectedBusiness.businessType.includes('项目管理')
        ) {
          form.value.courseName = 'pmp_course'
        } else if (
          selectedBusiness.businessType.includes('Python') ||
          selectedBusiness.businessType.includes('编程')
        ) {
          form.value.courseName = 'python_course'
        } else if (
          selectedBusiness.businessType.includes('母婴') ||
          selectedBusiness.businessType.includes('护理')
        ) {
          form.value.courseName = 'maternal_care'
        } else if (
          selectedBusiness.businessType.includes('营养') ||
          selectedBusiness.businessType.includes('师')
        ) {
          form.value.courseName = 'nutritionist'
        } else if (
          selectedBusiness.businessType.includes('心理') ||
          selectedBusiness.businessType.includes('咨询')
        ) {
          form.value.courseName = 'psychologist'
        } else if (
          selectedBusiness.businessType.includes('数据') ||
          selectedBusiness.businessType.includes('分析')
        ) {
          form.value.courseName = 'data_analyst'
        }
      }

      // 商机选择时不自动设置来源，来源由线索选择决定

      console.log('选择的商机:', selectedBusiness)
    }
  } else {
    // 清空商机选择时，重置相关字段
    form.value.courseName = 'pmp_course'
    form.value.orderSource = ''
  }
}

/** 处理线索选择 */
const handleLeadChange = (value: string) => {
  if (value) {
    const selectedLead = leadOptions.value.find((item) => item.id === value)
    if (selectedLead) {
      // 自动填充学员信息
      form.value.studentName = selectedLead.customerName || ''

      // 根据业务模块自动选择课程类型
      if (selectedLead.businessModule) {
        if (
          selectedLead.businessModule.includes('PMP') ||
          selectedLead.businessModule.includes('项目管理')
        ) {
          form.value.orderType = 'certification'
          form.value.courseName = 'pmp_course'
        } else if (
          selectedLead.businessModule.includes('Python') ||
          selectedLead.businessModule.includes('编程')
        ) {
          form.value.orderType = 'training'
          form.value.courseName = 'python_course'
        } else if (
          selectedLead.businessModule.includes('母婴') ||
          selectedLead.businessModule.includes('护理')
        ) {
          form.value.orderType = 'certification'
          form.value.courseName = 'maternal_care'
        } else if (
          selectedLead.businessModule.includes('营养') ||
          selectedLead.businessModule.includes('师')
        ) {
          form.value.orderType = 'certification'
          form.value.courseName = 'nutritionist'
        } else if (
          selectedLead.businessModule.includes('心理') ||
          selectedLead.businessModule.includes('咨询')
        ) {
          form.value.orderType = 'training'
          form.value.courseName = 'psychologist'
        } else if (
          selectedLead.businessModule.includes('数据') ||
          selectedLead.businessModule.includes('分析')
        ) {
          form.value.orderType = 'training'
          form.value.courseName = 'data_analyst'
        }
      }

      // 根据线索来源自动选择订单来源
      if (selectedLead.leadSource) {
        form.value.orderSource = selectedLead.leadSource
        console.log('线索选择时设置订单来源:', selectedLead.leadSource)
      }

      console.log('选择的线索:', selectedLead)
    }
  } else {
    // 清空线索选择时，重置相关字段
    form.value.studentName = ''
    form.value.orderType = 'certification'
    form.value.courseName = 'pmp_course'
    form.value.orderSource = ''
  }
}

/** 获取选中的商机信息 */
const getSelectedBusinessInfo = (): string => {
  if (!form.value.businessOpportunity) return ''
  const selectedBusiness = businessOptions.value.find(
    (item) => item.id === form.value.businessOpportunity
  )
  if (selectedBusiness) {
    return `${selectedBusiness.name} - ${selectedBusiness.customerName} - ${selectedBusiness.businessType}`
  }
  return ''
}

/** 获取选中的线索信息 */
const getSelectedLeadInfo = (): string => {
  if (!form.value.associatedLead) return ''
  const selectedLead = leadOptions.value.find((item) => item.id === form.value.associatedLead)
  if (selectedLead) {
    return `${selectedLead.leadId} - ${selectedLead.customerName} - ${selectedLead.businessModule}`
  }
  return ''
}

/** 从线索数据中更新订单来源 */
const updateOrderSourceFromLead = async (leadId: number | undefined) => {
  if (!leadId) return

  // 如果线索数据还没有加载，等待加载完成
  if (leadOptions.value.length === 0) {
    console.log('线索数据未加载，等待数据加载完成...')
    await fetchDropdownData()
  }

  const associatedLead = leadOptions.value.find((item) => item.id === leadId)
  if (associatedLead && associatedLead.leadSource) {
    form.value.orderSource = associatedLead.leadSource
    console.log('从关联线索获取最新来源:', associatedLead.leadSource)
  } else {
    console.log('未找到关联线索或线索来源信息')
  }
}

/** 获取文件名 */
const getFileName = (url: string): string => {
  if (!url) return ''
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const fileName = pathname.split('/').pop() || ''
    return decodeURIComponent(fileName)
  } catch (error) {
    // 如果不是有效的URL，直接返回原字符串
    return url.split('/').pop() || url
  }
}

/** 下载附件 */
const downloadAttachment = (url: string) => {
  if (!url) {
    ElMessage.warning('附件地址无效')
    return
  }

  try {
    const link = document.createElement('a')
    link.href = url
    link.download = getFileName(url)
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载附件')
  } catch (error) {
    console.error('下载附件失败:', error)
    ElMessage.error('下载附件失败，请重试')
  }
}

/** 删除附件 */
const removeAttachment = () => {
  ElMessageBox.confirm('确定要删除当前附件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      form.value.contractAttachment = ''
      fileList.value = []
      ElMessage.success('附件已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 在提交前检查合同附件状态
    console.log('=== 表单提交前检查 ===')
    console.log('当前表单数据:', form.value)
    console.log('合同附件字段值:', form.value.contractAttachment)
    console.log('文件列表状态:', fileList.value)

    await formRef.value.validate()
    loading.value = true

    // 获取线索的 customerPhone 字段，下沉到 student_oneid
    let studentOneid = ''
    if (form.value.associatedLead) {
      const selectedLead = leadOptions.value.find((item) => item.id === form.value.associatedLead)
      if (selectedLead && selectedLead.customerPhone) {
        studentOneid = selectedLead.customerPhone
        console.log('从线索下沉 customerPhone 到 student_oneid:', studentOneid)
      }
    }

    if (isEdit.value) {
      // 编辑模式，调用更新接口
      if (!form.value.id) {
        ElMessage.error('订单ID不能为空，请检查数据')
        return
      }

      console.log('编辑模式，订单ID:', form.value.id)

      await IndividualTrainingOrderApi.updateOrder({
        id: form.value.id,
        businessOpportunity: form.value.businessOpportunity?.toString(),
        associatedLead: form.value.associatedLead?.toString(),
        orderType: form.value.orderType,
        studentName: form.value.studentName,
        courseName: form.value.courseName,
        orderSource: form.value.orderSource,
        orderAmount: parseFloat(form.value.orderAmount),
        paymentStatus: form.value.paymentStatus,
        learningStatus: form.value.learningStatus,
        collectionAmount: form.value.collectionAmount
          ? parseFloat(form.value.collectionAmount)
          : undefined,
        collectionMethod: form.value.collectionMethod,
        collectionDate: form.value.collectionDate,
        remark: form.value.collectionRemarks,
        // 添加合同相关信息
        contractType: form.value.contractType,
        contractTemplate:
          form.value.contractType === 'electronic' ? form.value.contractTemplate : undefined,
        contractAttachment:
          form.value.contractType === 'paper' ? form.value.contractAttachment : undefined,
        contractNumber: form.value.contractType === 'paper' ? form.value.contractNumber : undefined,
        contractName: form.value.contractType === 'paper' ? form.value.contractName : undefined,
        signingDate: form.value.contractType === 'paper' ? form.value.signingDate : undefined,
        contractAmount: form.value.contractType === 'paper' ? form.value.contractAmount : undefined,
        // 下沉线索的 customerPhone 到 student_oneid
        studentOneid: studentOneid
      })
      ElMessage.success('更新成功')
    } else {
      // 新增模式，调用新增接口
      console.log('新增模式')

      await IndividualTrainingOrderApi.addOrder({
        businessOpportunity: form.value.businessOpportunity?.toString(),
        associatedLead: form.value.associatedLead?.toString(),
        orderType: form.value.orderType,
        studentName: form.value.studentName,
        courseName: form.value.courseName,
        orderSource: form.value.orderSource,
        orderAmount: parseFloat(form.value.orderAmount),
        paymentStatus: form.value.paymentStatus,
        learningStatus: form.value.learningStatus,
        collectionAmount: form.value.collectionAmount
          ? parseFloat(form.value.collectionAmount)
          : undefined,
        collectionMethod: form.value.collectionMethod,
        collectionDate: form.value.collectionDate,
        remark: form.value.collectionRemarks,
        // 添加合同相关信息
        contractType: form.value.contractType,
        contractTemplate:
          form.value.contractType === 'electronic' ? form.value.contractTemplate : undefined,
        contractAttachment:
          form.value.contractType === 'paper' ? form.value.contractAttachment : undefined,
        contractNumber: form.value.contractType === 'paper' ? form.value.contractNumber : undefined,
        contractName: form.value.contractType === 'paper' ? form.value.contractName : undefined,
        signingDate: form.value.contractType === 'paper' ? form.value.signingDate : undefined,
        contractAmount: form.value.contractType === 'paper' ? form.value.contractAmount : undefined,
        // 下沉线索的 customerPhone 到 student_oneid
        studentOneid: studentOneid
      })
      ElMessage.success('保存成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

/** 重置表单 */
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 清理文件列表
  fileList.value = []
  initFormData()
}

/** 关闭抽屉 */
const handleClose = () => {
  try {
    // 安全地重置表单
    if (formRef.value) {
      formRef.value.resetFields()
    }

    // 清理文件列表
    fileList.value = []

    // 重置表单数据
    initFormData()

    // 关闭弹窗
    emit('update:visible', false)
  } catch (error) {
    console.error('关闭抽屉时发生错误:', error)
    // 即使出错也要关闭弹窗
    emit('update:visible', false)
  }
}

/** 初始化表单数据 */
const initFormData = () => {
  form.value = {
    id: undefined,
    businessOpportunity: undefined,
    associatedLead: undefined,
    orderType: 'certification',
    studentName: '',
    courseName: 'pmp_course',
    orderSource: '',
    orderAmount: '',
    paymentStatus: 'pending',
    learningStatus: 'learning_pending_exam',
    contractType: 'paper',
    contractTemplate: 'training_contract',
    collectionAmount: '',
    collectionMethod: '',
    collectionDate: '',
    operator: '',
    collectionRemarks: '',
    contractAttachment: '',
    contractNumber: '',
    contractName: '',
    signingDate: '',
    contractAmount: ''
  }
}

/** 编辑时回显数据 */
const setEditData = async (data: any) => {
  if (data && data.id) {
    console.log('=== 编辑数据回显开始 ===')
    console.log('接收到的编辑数据:', data)

    // 确保商机和线索字段正确回显
    const editData = {
      ...data,
      // 确保ID字段正确
      id: data.id || data.orderId,
      // 确保商机和线索字段为数字类型
      businessOpportunity:
        data.opportunityId || data.businessOpportunity
          ? parseInt(data.opportunityId || data.businessOpportunity)
          : undefined,
      associatedLead:
        data.leadId || data.associatedLead
          ? parseInt(data.leadId || data.associatedLead)
          : undefined,
      // 确保金额字段为字符串（表单期望的格式）
      orderAmount: data.orderAmount ? data.orderAmount.toString() : '',
      // 支付信息回写 - 从paymentList获取支付信息
      collectionAmount:
        data.paymentList && data.paymentList.length > 0
          ? data.paymentList[0].paymentAmount?.toString() || ''
          : data.collectionAmount
            ? data.collectionAmount.toString()
            : '',
      collectionMethod:
        data.paymentList && data.paymentList.length > 0
          ? data.paymentList[0].paymentType || ''
          : data.collectionMethod || '',
      collectionDate:
        data.paymentList && data.paymentList.length > 0
          ? new Date(data.paymentList[0].paymentTime).toISOString().slice(0, 19).replace('T', ' ')
          : data.collectionDate || '',
      operator:
        data.paymentList && data.paymentList.length > 0
          ? data.paymentList[0].operatorName || ''
          : data.operator || '',
      collectionRemarks:
        data.paymentList && data.paymentList.length > 0
          ? data.paymentList[0].paymentRemark || ''
          : data.collectionRemarks || '',
      // 确保合同金额字段为字符串
      contractAmount:
        data.contractAmount || data.contractInfo?.amount
          ? (data.contractAmount || data.contractInfo?.amount).toString()
          : '',
      signingDate:
        data.signingDate || data.contractInfo?.startDate
          ? data.signingDate ||
            `${data.contractInfo.startDate[0]}-${String(data.contractInfo.startDate[1]).padStart(2, '0')}-${String(data.contractInfo.startDate[2]).padStart(2, '0')}`
          : '',
      // 合同相关信息映射到现有字段
      contractNumber: data.contractNumber || data.contractInfo?.contractNumber || '',
      contractName: data.contractName || data.contractInfo?.contractName || '',
      contractAttachment:
        data.contractFileUrl || data.contractAttachment || data.contractInfo?.attachmentPath || '',
      // 学员信息映射
      studentOneid: data.studentOneid || '',
      // 课程信息映射
      courseName: data.courseName || '',
      // 订单类型映射 - 修复映射逻辑
      orderType:
        data.orderType === 'personal-training'
          ? 'training'
          : data.orderType === 'certification'
            ? 'certification'
            : data.orderType || 'certification',
      // 订单来源映射 - 优先从线索来源获取，其次从订单来源获取
      orderSource: data.leadSource || data.orderSource || '',
      // 支付状态映射
      paymentStatus: data.paymentStatus || 'pending',
      // 学习状态映射
      learningStatus: data.learningStatus || 'learning_pending_exam',
      // 合同类型映射 - 修复映射逻辑
      contractType:
        data.contractType === 'electronic'
          ? 'electronic'
          : data.contractType === 'paper'
            ? 'paper'
            : data.contractType || 'paper',
      // 合同模板映射
      contractTemplate: data.contractTemplate || 'training_contract',
      // 添加更多字段映射
      examStatus: data.examStatus || 'not_registered',
      courseType: data.courseType || '',
      remark: data.remark || '',
      // 商机线索相关
      opportunityId: data.opportunityId || data.businessOpportunity,
      leadId: data.leadId || data.associatedLead,
      // 项目信息
      projectName: data.projectName || '',
      projectDescription: data.projectDescription || '',
      // 结算信息
      settlementStatus: data.settlementStatus || 'pending',
      settlementTime: data.settlementTime || '',
      settlementMethod: data.settlementMethod || '',
      approvalLevel: data.approvalLevel || '0'
    }

    console.log('处理后的编辑数据:', editData)
    console.log('关键字段映射详情:')
    console.log('- 原始 orderType:', data.orderType, '-> 映射后:', editData.orderType)
    console.log('- 原始 orderSource:', data.orderSource, '-> 映射后:', editData.orderSource)
    console.log('- 原始 contractType:', data.contractType, '-> 映射后:', editData.contractType)
    console.log('- 原始 leadSource:', data.leadSource)
    console.log('- 原始 businessLine:', data.businessLine)

    // 设置表单数据
    form.value = editData
    isEdit.value = true

    // 编辑模式下，如果有关联线索，尝试从线索数据中获取最新的来源信息
    await updateOrderSourceFromLead(editData.associatedLead)

    console.log('=== 编辑数据回显完成 ===')
    console.log('当前表单数据:', form.value)
  } else {
    console.log('没有编辑数据或ID，初始化表单为新增模式')
    initFormData()
    isEdit.value = false
  }
}

// 监听器
watch(
  () => props.editData,
  (newVal) => {
    console.log('editData 变化:', newVal)
    if (newVal && newVal.id) {
      nextTick(() => {
        setEditData(newVal)
      })
    } else {
      console.log('没有有效的编辑数据，重置为新增模式')
      nextTick(() => {
        initFormData()
        isEdit.value = false
      })
    }
  },
  { immediate: true }
)

// 组件挂载时获取下拉数据
onMounted(() => {
  fetchDropdownData()
})

// 组件卸载前清理数据
onBeforeUnmount(() => {
  try {
    // 清理文件列表
    fileList.value = []

    // 重置表单数据
    initFormData()

    // 清理引用
    formRef.value = undefined
  } catch (error) {
    console.error('组件卸载时清理数据发生错误:', error)
  }
})

watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      handleReset()
    }
  }
)

// 监听支付状态变化
watch(
  () => form.value.paymentStatus,
  (newVal) => {
    // 当支付状态改变时，清除支付信息字段的验证错误
    nextTick(() => {
      if (formRef.value && newVal !== 'paid') {
        // 如果不是已支付状态，清除支付信息字段的验证错误
        formRef.value.clearValidate([
          'collectionAmount',
          'collectionMethod',
          'collectionDate',
          'operator'
        ])
      }
    })
  }
)
</script>

<style scoped lang="scss">
.individual-training-form {
  padding: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin: 20px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 8px;
      color: #67c23a;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .selected-info {
    margin-top: 8px;

    .el-tag {
      width: 100%;
      text-align: center;
      padding: 8px 12px;
      border-radius: 4px;
    }
  }

  .existing-attachment {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;

    .attachment-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .attachment-icon {
        color: #409eff;
        margin-right: 8px;
        font-size: 18px;
      }

      .attachment-name {
        color: #606266;
        font-size: 14px;
        word-break: break-all;
      }
    }

    .attachment-actions {
      display: flex;
      gap: 8px;
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.upload-demo {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}
</style>
