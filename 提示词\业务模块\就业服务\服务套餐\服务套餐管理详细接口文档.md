## 获取服务套餐详情

**接口地址**:`/publicbiz/employment/service-package/{id}`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema                           |
| ------ | ---- | -------------------------------- |
| 200    | OK   | CommonResultServicePackageRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | ServicePackageRespVO | ServicePackageRespVO |
| &emsp;&emsp;id | 主键，自增 | integer(int64) |  |
| &emsp;&emsp;name | 套餐名称 | string |  |
| &emsp;&emsp;category | 服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理 | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL | string |  |
| &emsp;&emsp;price | 套餐价格 | number |  |
| &emsp;&emsp;originalPrice | 原价 | number |  |
| &emsp;&emsp;unit | 价格单位：次/项/天/月 | string |  |
| &emsp;&emsp;serviceDuration | 服务时长，如：4小时、26天、90天 | string |  |
| &emsp;&emsp;packageType | 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐 | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 | string |  |
| &emsp;&emsp;serviceDescription | 服务描述，建议100-200字 | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容，富文本格式 | string |  |
| &emsp;&emsp;serviceProcess | 服务流程，富文本格式 | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 | string |  |
| &emsp;&emsp;status | 状态：active-已上架/pending-待上架/deleted-回收站 | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围：1-提前1天/3-提前3天/7-提前7天 | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式：fixed-固定时间/flexible-灵活时间 | string |  |
| &emsp;&emsp;appointmentMode | 预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数 | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始 | string |  |
| &emsp;&emsp;addressSetting | 地址设置：fixed-固定地址/changeable-可变更地址 | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 | string |  |
| &emsp;&emsp;auditStatus | 审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝 | string |  |
| &emsp;&emsp;agencyId | 所属机构ID | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 | string |  |
| &emsp;&emsp;partnerId | 合作伙伴ID | integer(int64) |  |
| &emsp;&emsp;partnerName | 合作伙伴名称 | string |  |
| &emsp;&emsp;rejectReason | 拒绝原因，审核拒绝时填写 | string |  |
| &emsp;&emsp;categoryId | 服务分类ID | integer(int64) |  |
| &emsp;&emsp;serviceTimeStart | 服务开始时间，如：09:00:00 | string |  |
| &emsp;&emsp;serviceTimeEnd | 服务结束时间，如：13:00:00 | string |  |
| &emsp;&emsp;restDayType | 休息日类型：none-无特殊设置/saturday-周六/sunday-周日/weekend-周末/statutory-法定节假日/both-周末及法定节假日/negotiable-客户协商 | string |  |
| &emsp;&emsp;serviceTimespan | 服务时间，如：9:00-13:00、全天、夜班 | string |  |
| &emsp;&emsp;serviceTimes | 服务次数(次)或服务周期(天)，如：4、6、10 | integer(int32) |  |
| &emsp;&emsp;validityPeriod | 有效期（天），如：90天、180天、365天 | integer(int32) |  |
| &emsp;&emsp;validityPeriodUnit | 有效期单位：day-天/week-周/month-月/year-年 | string |  |
| &emsp;&emsp;serviceIntervalType | 服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年 | string |  |
| &emsp;&emsp;serviceIntervalValue | 服务间隔数值/服务频次数值，如：1表示每周1次或每月1次 | integer(int32) |  |
| &emsp;&emsp;singleDurationHours | 单次服务时长（小时），如：2、4、6 | integer(int32) |  |
| &emsp;&emsp;carouselList | 轮播图列表 | array | ServicePackageCarouselRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;featureList | 特色标签列表 | array | ServicePackageFeatureRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"id": 0,
		"name": "",
		"category": "",
		"thumbnail": "",
		"price": 0,
		"originalPrice": 0,
		"unit": "",
		"serviceDuration": "",
		"packageType": "",
		"taskSplitRule": "",
		"serviceDescription": "",
		"serviceDetails": "",
		"serviceProcess": "",
		"purchaseNotice": "",
		"status": "",
		"advanceBookingDays": 0,
		"timeSelectionMode": "",
		"appointmentMode": "",
		"serviceStartTime": "",
		"addressSetting": "",
		"maxBookingDays": 0,
		"cancellationPolicy": "",
		"carouselList": [
			{
				"id": 0,
				"packageId": 0,
				"imageUrl": "",
				"sortOrder": 0,
				"status": 0,
				"createTime": "",
				"updateTime": ""
			}
		],
		"featureList": [
			{
				"id": 0,
				"packageId": 0,
				"featureName": "",
				"sortOrder": 0,
				"createTime": "",
				"updateTime": ""
			}
		],
		"createTime": "",
		"updateTime": ""
	},
	"msg": ""
}
```

## 删除服务套餐（软删除）

**接口地址**:`/admin-api/publicbiz/employment/service-package/{id}`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 新增服务套餐

**接口地址**:`/publicbiz/employment/service-package/create`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "name": "",
  "category": "",
  "thumbnail": "",
  "price": 0,
  "originalPrice": 0,
  "unit": "",
  "serviceDuration": "",
  "packageType": "",
  "taskSplitRule": "",
  "serviceDescription": "",
  "serviceDetails": "",
  "serviceProcess": "",
  "purchaseNotice": "",
  "status": "",
  "advanceBookingDays": 0,
  "timeSelectionMode": "",
  "appointmentMode": "",
  "serviceStartTime": "",
  "addressSetting": "",
  "maxBookingDays": 0,
  "cancellationPolicy": "",
  "agencyId": 0,
  "agencyName": "",
  "partnerId": 0,
  "partnerName": "",
  "categoryId": 0,
  "serviceTimeStart": "",
  "serviceTimeEnd": "",
  "restDayType": "",
  "serviceTimespan": "",
  "serviceTimes": 0,
  "validityPeriod": 0,
  "validityPeriodUnit": "",
  "serviceIntervalType": "",
  "serviceIntervalValue": 0,
  "singleDurationHours": 0,
  "carouselList": [
    {
      "id": 0,
      "imageUrl": "",
      "sortOrder": 0,
      "status": 0
    }
  ],
  "featureList": [
    {
      "id": 0,
      "featureName": "",
      "sortOrder": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageSaveReqVO | 就业服务-服务套餐新增 Request VO | body | true | ServicePackageSaveReqVO | ServicePackageSaveReqVO |
| &emsp;&emsp;name | 套餐名称 |  | true | string |  |
| &emsp;&emsp;category | 服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理 |  | true | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL |  | false | string |  |
| &emsp;&emsp;price | 套餐价格 |  | true | number |  |
| &emsp;&emsp;originalPrice | 原价 |  | false | number |  |
| &emsp;&emsp;unit | 价格单位：次/项/天/月 |  | true | string |  |
| &emsp;&emsp;serviceDuration | 服务时长，如：4小时、26天、90天 |  | false | string |  |
| &emsp;&emsp;packageType | 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐 |  | true | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 |  | false | string |  |
| &emsp;&emsp;serviceDescription | 服务描述，建议100-200字 |  | false | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容，富文本格式 |  | false | string |  |
| &emsp;&emsp;serviceProcess | 服务流程，富文本格式 |  | false | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 |  | false | string |  |
| &emsp;&emsp;status | 状态：active-已上架/pending-待上架/deleted-回收站 |  | false | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围：1-提前1天/3-提前3天/7-提前7天 |  | false | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式：fixed-固定时间/flexible-灵活时间 |  | false | string |  |
| &emsp;&emsp;appointmentMode | 预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数 |  | false | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始 |  | false | string |  |
| &emsp;&emsp;addressSetting | 地址设置：fixed-固定地址/changeable-可变更地址 |  | false | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 |  | false | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 |  | false | string |  |
| &emsp;&emsp;agencyId | 所属机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 |  | false | string |  |
| &emsp;&emsp;partnerId | 合作伙伴ID |  | false | integer(int64) |  |
| &emsp;&emsp;partnerName | 合作伙伴名称 |  | false | string |  |
| &emsp;&emsp;categoryId | 服务分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;serviceTimeStart | 服务开始时间，如：09:00:00 |  | false | string |  |
| &emsp;&emsp;serviceTimeEnd | 服务结束时间，如：13:00:00 |  | false | string |  |
| &emsp;&emsp;restDayType | 休息日类型：none-无特殊设置/saturday-周六/sunday-周日/weekend-周末/statutory-法定节假日/both-周末及法定节假日/negotiable-客户协商 |  | false | string |  |
| &emsp;&emsp;serviceTimespan | 服务时间，如：9:00-13:00、全天、夜班 |  | false | string |  |
| &emsp;&emsp;serviceTimes | 服务次数(次)或服务周期(天)，如：4、6、10 |  | false | integer(int32) |  |
| &emsp;&emsp;validityPeriod | 有效期（天），如：90天、180天、365天 |  | false | integer(int32) |  |
| &emsp;&emsp;validityPeriodUnit | 有效期单位：day-天/week-周/month-月/year-年 |  | false | string |  |
| &emsp;&emsp;serviceIntervalType | 服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年 |  | false | string |  |
| &emsp;&emsp;serviceIntervalValue | 服务间隔数值/服务频次数值，如：1表示每周1次或每月1次 |  | false | integer(int32) |  |
| &emsp;&emsp;singleDurationHours | 单次服务时长（小时），如：2、4、6 |  | false | integer(int32) |  |
| &emsp;&emsp;carouselList | 轮播图列表 |  | false | array | ServicePackageCarouselSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |
| &emsp;&emsp;featureList | 特色标签列表 |  | false | array | ServicePackageFeatureSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema           |
| ------ | ---- | ---------------- |
| 200    | OK   | CommonResultLong |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | integer(int64) | integer(int64) |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```

## 获取服务套餐分页

**接口地址**:`/publicbiz/employment/service-package/page`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| pageNo | 页码，从 1 开始 | query | true | string |  |
| pageSize | 每页条数，最大值为 100 | query | true | string |  |
| keyword | 套餐名称或ID关键词，纯数字时精确匹配套餐ID，非纯数字时模糊匹配套餐名称 | query | false | string |  |
| category | 服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理 | query | false | string |  |
| categoryId | 服务分类ID，用于精确过滤特定分类的服务套餐 | query | false | integer(int64) |  |
| status | 状态：active-已上架/pending-待上架/deleted-回收站 | query | false | string |  |
| packageType | 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐 | query | false | string |  |
| partnerId | 所属合作伙伴筛选条件 | query | false | integer(int64) |  |

**响应状态**:

| 状态码 | 说明 | schema                                     |
| ------ | ---- | ------------------------------------------ |
| 200    | OK   | CommonResultPageResultServicePackageRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | PageResultServicePackageRespVO | PageResultServicePackageRespVO |
| &emsp;&emsp;list | 数据 | array | ServicePackageRespVO |
| &emsp;&emsp;&emsp;&emsp;id | 主键，自增 | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;name | 套餐名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;category | 服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理 | string |  |
| &emsp;&emsp;&emsp;&emsp;thumbnail | 套餐主图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;price | 套餐价格 | number |  |
| &emsp;&emsp;&emsp;&emsp;originalPrice | 原价 | number |  |
| &emsp;&emsp;&emsp;&emsp;unit | 价格单位：次/项/天/月 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDuration | 服务时长，如：4小时、26天、90天 | string |  |
| &emsp;&emsp;&emsp;&emsp;packageType | 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐 | string |  |
| &emsp;&emsp;&emsp;&emsp;taskSplitRule | 任务拆分规则 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDescription | 服务描述，建议100-200字 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceDetails | 详细服务内容，富文本格式 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceProcess | 服务流程，富文本格式 | string |  |
| &emsp;&emsp;&emsp;&emsp;purchaseNotice | 购买须知 | string |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态：active-已上架/pending-待上架/deleted-回收站 | string |  |
| &emsp;&emsp;&emsp;&emsp;advanceBookingDays | 预约时间范围：1-提前1天/3-提前3天/7-提前7天 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;timeSelectionMode | 时间选择模式：fixed-固定时间/flexible-灵活时间 | string |  |
| &emsp;&emsp;&emsp;&emsp;appointmentMode | 预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceStartTime | 服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始 | string |  |
| &emsp;&emsp;&emsp;&emsp;addressSetting | 地址设置：fixed-固定地址/changeable-可变更地址 | string |  |
| &emsp;&emsp;&emsp;&emsp;maxBookingDays | 最大预约天数 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;cancellationPolicy | 取消政策 | string |  |
| &emsp;&emsp;&emsp;&emsp;auditStatus | 审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝 | string |  |
| &emsp;&emsp;&emsp;&emsp;agencyId | 所属机构ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;agencyName | 所属机构名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;partnerId | 合作伙伴ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;partnerName | 合作伙伴名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;rejectReason | 拒绝原因，审核拒绝时填写 | string |  |
| &emsp;&emsp;&emsp;&emsp;categoryId | 服务分类ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;serviceTimeStart | 服务开始时间，如：09:00:00 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceTimeEnd | 服务结束时间，如：13:00:00 | string |  |
| &emsp;&emsp;&emsp;&emsp;restDayType | 休息日类型：none-无特殊设置/saturday-周六/sunday-周日/weekend-周末/statutory-法定节假日/both-周末及法定节假日/negotiable-客户协商 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceTimespan | 服务时间，如：9:00-13:00、全天、夜班 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceTimes | 服务次数(次)或服务周期(天)，如：4、6、10 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;validityPeriod | 有效期（天），如：90天、180天、365天 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;validityPeriodUnit | 有效期单位：day-天/week-周/month-月/year-年 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceIntervalType | 服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年 | string |  |
| &emsp;&emsp;&emsp;&emsp;serviceIntervalValue | 服务间隔数值/服务频次数值，如：1表示每周1次或每月1次 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;singleDurationHours | 单次服务时长（小时），如：2、4、6 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;carouselList | 轮播图列表 | array | ServicePackageCarouselRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 轮播图ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;status | 状态 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;featureList | 特色标签列表 | array | ServicePackageFeatureRespVO |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id | 特色标签ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;packageId | 套餐ID | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 | string |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder | 排序 | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;createTime | 创建时间 | string(date-time) |  |
| &emsp;&emsp;&emsp;&emsp;updateTime | 更新时间 | string(date-time) |  |
| &emsp;&emsp;total | 总量 | integer(int64) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 0,
				"name": "",
				"category": "",
				"thumbnail": "",
				"price": 0,
				"originalPrice": 0,
				"unit": "",
				"serviceDuration": "",
				"packageType": "",
				"taskSplitRule": "",
				"serviceDescription": "",
				"serviceDetails": "",
				"serviceProcess": "",
				"purchaseNotice": "",
				"status": "",
				"advanceBookingDays": 0,
				"timeSelectionMode": "",
				"appointmentMode": "",
				"serviceStartTime": "",
				"addressSetting": "",
				"maxBookingDays": 0,
				"cancellationPolicy": "",
				"carouselList": [
					{
						"id": 0,
						"packageId": 0,
						"imageUrl": "",
						"sortOrder": 0,
						"status": 0,
						"createTime": "",
						"updateTime": ""
					}
				],
				"featureList": [
					{
						"id": 0,
						"packageId": 0,
						"featureName": "",
						"sortOrder": 0,
						"createTime": "",
						"updateTime": ""
					}
				],
				"createTime": "",
				"updateTime": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```

## 批量更新服务套餐状态

**接口地址**:`/publicbiz/employment/service-package/status`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "ids": [],
  "status": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageStatusUpdateReqVO | 就业服务-服务套餐状态更新 Request VO | body | true | ServicePackageStatusUpdateReqVO | ServicePackageStatusUpdateReqVO |
| &emsp;&emsp;ids | 套餐ID列表 |  | true | array | integer(int64) |
| &emsp;&emsp;status | 目标状态 |  | true | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 更新服务套餐

**接口地址**:`/publicbiz/employment/service-package/update`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "id": 0,
  "name": "",
  "category": "",
  "thumbnail": "",
  "price": 0,
  "originalPrice": 0,
  "unit": "",
  "serviceDuration": "",
  "packageType": "",
  "taskSplitRule": "",
  "serviceDescription": "",
  "serviceDetails": "",
  "serviceProcess": "",
  "purchaseNotice": "",
  "status": "",
  "advanceBookingDays": 0,
  "timeSelectionMode": "",
  "appointmentMode": "",
  "serviceStartTime": "",
  "addressSetting": "",
  "maxBookingDays": 0,
  "cancellationPolicy": "",
  "agencyId": 0,
  "agencyName": "",
  "partnerId": 0,
  "partnerName": "",
  "categoryId": 0,
  "serviceTimeStart": "",
  "serviceTimeEnd": "",
  "restDayType": "",
  "serviceTimespan": "",
  "serviceTimes": 0,
  "validityPeriod": 0,
  "validityPeriodUnit": "",
  "serviceIntervalType": "",
  "serviceIntervalValue": 0,
  "singleDurationHours": 0,
  "carouselList": [
    {
      "id": 0,
      "imageUrl": "",
      "sortOrder": 0,
      "status": 0
    }
  ],
  "featureList": [
    {
      "id": 0,
      "featureName": "",
      "sortOrder": 0
    }
  ]
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| servicePackageUpdateReqVO | 就业服务-服务套餐更新 Request VO | body | true | ServicePackageUpdateReqVO | ServicePackageUpdateReqVO |
| &emsp;&emsp;id | 主键，自增 |  | true | integer(int64) |  |
| &emsp;&emsp;name | 套餐名称 |  | true | string |  |
| &emsp;&emsp;category | 服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理 |  | true | string |  |
| &emsp;&emsp;thumbnail | 套餐主图URL |  | false | string |  |
| &emsp;&emsp;price | 套餐价格 |  | true | number |  |
| &emsp;&emsp;originalPrice | 原价 |  | false | number |  |
| &emsp;&emsp;unit | 价格单位：次/项/天/月 |  | true | string |  |
| &emsp;&emsp;serviceDuration | 服务时长，如：4小时、26天、90天 |  | false | string |  |
| &emsp;&emsp;packageType | 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐 |  | true | string |  |
| &emsp;&emsp;taskSplitRule | 任务拆分规则 |  | false | string |  |
| &emsp;&emsp;serviceDescription | 服务描述，建议100-200字 |  | false | string |  |
| &emsp;&emsp;serviceDetails | 详细服务内容，富文本格式 |  | false | string |  |
| &emsp;&emsp;serviceProcess | 服务流程，富文本格式 |  | false | string |  |
| &emsp;&emsp;purchaseNotice | 购买须知 |  | false | string |  |
| &emsp;&emsp;status | 状态：active-已上架/pending-待上架/deleted-回收站 |  | false | string |  |
| &emsp;&emsp;advanceBookingDays | 预约时间范围：1-提前1天/3-提前3天/7-提前7天 |  | false | integer(int32) |  |
| &emsp;&emsp;timeSelectionMode | 时间选择模式：fixed-固定时间/flexible-灵活时间 |  | false | string |  |
| &emsp;&emsp;appointmentMode | 预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数 |  | false | string |  |
| &emsp;&emsp;serviceStartTime | 服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始 |  | false | string |  |
| &emsp;&emsp;addressSetting | 地址设置：fixed-固定地址/changeable-可变更地址 |  | false | string |  |
| &emsp;&emsp;maxBookingDays | 最大预约天数 |  | false | integer(int32) |  |
| &emsp;&emsp;cancellationPolicy | 取消政策 |  | false | string |  |
| &emsp;&emsp;agencyId | 所属机构ID |  | false | integer(int64) |  |
| &emsp;&emsp;agencyName | 所属机构名称 |  | false | string |  |
| &emsp;&emsp;partnerId | 合作伙伴ID |  | false | integer(int64) |  |
| &emsp;&emsp;partnerName | 合作伙伴名称 |  | false | string |  |
| &emsp;&emsp;categoryId | 服务分类ID |  | false | integer(int64) |  |
| &emsp;&emsp;serviceTimeStart | 服务开始时间，如：09:00:00 |  | false | string |  |
| &emsp;&emsp;serviceTimeEnd | 服务结束时间，如：13:00:00 |  | false | string |  |
| &emsp;&emsp;restDayType | 休息日类型：none-无特殊设置/saturday-周六/sunday-周日/weekend-周末/statutory-法定节假日/both-周末及法定节假日/negotiable-客户协商 |  | false | string |  |
| &emsp;&emsp;serviceTimespan | 服务时间，如：9:00-13:00、全天、夜班 |  | false | string |  |
| &emsp;&emsp;serviceTimes | 服务次数(次)或服务周期(天)，如：4、6、10 |  | false | integer(int32) |  |
| &emsp;&emsp;validityPeriod | 有效期（天），如：90天、180天、365天 |  | false | integer(int32) |  |
| &emsp;&emsp;validityPeriodUnit | 有效期单位：day-天/week-周/month-月/year-年 |  | false | string |  |
| &emsp;&emsp;serviceIntervalType | 服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年 |  | false | string |  |
| &emsp;&emsp;serviceIntervalValue | 服务间隔数值/服务频次数值，如：1表示每周1次或每月1次 |  | false | integer(int32) |  |
| &emsp;&emsp;singleDurationHours | 单次服务时长（小时），如：2、4、6 |  | false | integer(int32) |  |
| &emsp;&emsp;carouselList | 轮播图列表 |  | false | array | ServicePackageCarouselSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 轮播图ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;imageUrl | 轮播图URL |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |
| &emsp;&emsp;&emsp;&emsp;status | 状态 |  | false | integer(int32) |  |
| &emsp;&emsp;featureList | 特色标签列表 |  | false | array | ServicePackageFeatureSaveReqVO |
| &emsp;&emsp;&emsp;&emsp;id | 特色标签ID（更新时使用） |  | false | integer(int64) |  |
| &emsp;&emsp;&emsp;&emsp;featureName | 特色标签名称 |  | true | string |  |
| &emsp;&emsp;&emsp;&emsp;sortOrder | 排序 |  | false | integer(int32) |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 上传文件

**接口地址**:`/infra/file/upload`

**请求方式**:`POST`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:<p>模式一：后端上传文件</p>

**请求参数**:

**请求参数**:

| 参数名称  | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --------- | -------- | -------- | -------- | -------- | ------ |
| directory | 文件目录 | query    | false    | string   |        |

**响应状态**:

| 状态码 | 说明 | schema             |
| ------ | ---- | ------------------ |
| 200    | OK   | CommonResultString |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | string         |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": "",
	"msg": ""
}
```

## 移动服务套餐到回收站

**接口地址**:`/publicbiz/employment/service-package/{id}/move-to-recycle`

**请求方式**:`DELETE`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 申请上架服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/shelf`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 审核服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/audit`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded,application/json`

**响应数据类型**:`*/*`

**接口描述**:

**请求示例**:

```javascript
{
  "auditStatus": "",
  "rejectReason": ""
}
```

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| id |  | path | true | integer(int64) |  |
| servicePackageAuditStatusReqVO | 就业服务-服务套餐审核状态 Request VO | body | true | ServicePackageAuditStatusReqVO | ServicePackageAuditStatusReqVO |
| &emsp;&emsp;auditStatus | 审核状态 |  | true | string |  |
| &emsp;&emsp;rejectReason | 拒绝原因 |  | false | string |  |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 撤回服务套餐

**接口地址**:`/publicbiz/employment/service-package/{id}/withdraw`

**请求方式**:`PUT`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

**请求参数**:

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型       | schema |
| -------- | -------- | -------- | -------- | -------------- | ------ |
| id       |          | path     | true     | integer(int64) |        |

**响应状态**:

| 状态码 | 说明 | schema              |
| ------ | ---- | ------------------- |
| 200    | OK   | CommonResultBoolean |

**响应参数**:

| 参数名称 | 参数说明 | 类型           | schema         |
| -------- | -------- | -------------- | -------------- |
| code     |          | integer(int32) | integer(int32) |
| data     |          | boolean        |                |
| msg      |          | string         |                |

**响应示例**:

```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```

## 接口字段说明

### 任务拆分规则相关字段

根据前端页面实现，任务拆分规则字段的使用说明如下：

#### 长周期套餐任务拆分规则字段

当 `packageType` 为 `long-term`（长周期套餐）时，以下字段用于配置任务拆分规则：

| 字段名称 | 字段说明 | 数据类型 | 示例值 | 备注 |
| --- | --- | --- | --- | --- |
| serviceTimes | 服务次数(次)或服务周期(天) | integer | 4, 6, 10, 26, 30, 90 | 对应数据库注释：如：4、6、10 |
| serviceIntervalType | 服务间隔类型/服务频次类型 | string | "day", "weekly", "monthly", "year" | 对应数据库注释：day-每天/weekly-每周/monthly-每月/year-每年 |
| singleDurationHours | 单次服务时长（小时） | integer | 2, 4, 6, 8, 12, 24 | 对应数据库注释：如：2、4、6 |
| serviceTimespan | 服务时间 | string | "9:00-13:00", "全天", "夜班" | 对应数据库注释：如：9:00-13:00、全天、夜班 |
| restDayType | 休息日类型 | string | "none", "sunday", "statutory", "both", "negotiable" | 对应数据库注释：none-无特殊设置/sunday-周日/statutory-法定节假日/both-周日及法定节假日/negotiable-客户协商 |

#### 次数次卡套餐任务拆分规则字段

当 `packageType` 为 `count-card`（次数次卡套餐）时，以下字段用于配置任务拆分规则：

| 字段名称 | 字段说明 | 数据类型 | 示例值 | 备注 |
| --- | --- | --- | --- | --- |
| serviceTimes | 服务次数(次)或服务周期(天) | integer | 1, 2, 4, 6, 8, 10, 12 | 对应数据库注释：如：4、6、10 |
| singleDurationHours | 单次服务时长（小时） | integer | 2, 3, 4, 6, 8 | 对应数据库注释：如：2、4、6 |
| serviceIntervalType | 服务间隔类型/服务频次类型 | string | "weekly", "monthly" | 对应数据库注释：day-每天/weekly-每周/monthly-每月/year-每年 |
| validityPeriod | 有效期（天） | integer | 30, 90, 180, 365 | 对应数据库注释：如：90天、180天、365天 |

#### 机构相关字段

| 字段名称    | 字段说明     | 数据类型       | 示例值       | 备注                             |
| ----------- | ------------ | -------------- | ------------ | -------------------------------- |
| agencyId    | 所属机构ID   | integer(int64) | 1001         | 对应数据库表的 agency_id 字段    |
| agencyName  | 所属机构名称 | string         | "北京分公司" | 对应数据库表的 agency_name 字段  |
| partnerId   | 合作伙伴ID   | integer(int64) | 2001         | 对应数据库表的 partner_id 字段   |
| partnerName | 合作伙伴名称 | string         | "XX合作伙伴" | 对应数据库表的 partner_name 字段 |

### 接口变更说明

#### 1. 服务套餐列表查询接口变更

- **新增请求参数**：`agency`（所属机构筛选条件）
- **新增响应字段**：机构相关字段、任务拆分规则相关字段、审核状态相关字段

#### 2. 服务套餐新增/编辑接口变更

- **新增请求字段**：机构相关字段、任务拆分规则相关字段
- **字段使用规则**：根据 `packageType` 的值，使用对应的任务拆分规则字段

#### 3. 前端表单实现说明

- 长周期套餐和次数次卡套餐的任务拆分规则区域根据套餐类型动态显示
- 机构筛选条件在列表页面的搜索区域中实现
- 所有新增字段都已在前端表单中实现对应的输入控件

#### 4. 数据库表结构变更

- 新增 `agency_id`、`agency_name` 字段用于机构管理
- 新增 `partner_id`、`partner_name` 字段用于合作伙伴管理
- 新增多个任务拆分规则相关字段，支持长周期套餐和次数次卡套餐的不同配置需求

#### 5. 字段映射关系

- 前端表单中的"服务周期"对应数据库的 `service_times` 字段（长周期套餐）
- 前端表单中的"服务次数"对应数据库的 `service_times` 字段（次数次卡套餐）
- 前端表单中的"服务频次"对应数据库的 `service_interval_type` 字段
- 前端表单中的"单次服务时长"对应数据库的 `single_duration_hours` 字段
- 前端表单中的"服务时间"对应数据库的 `service_timespan` 字段
- 前端表单中的"休息日设置"对应数据库的 `rest_day_type` 字段
- 前端表单中的"服务间隔"对应数据库的 `service_interval_type` 字段
- 前端表单中的"有效期"对应数据库的 `validity_period` 字段

## 获取服务套餐统计报表

**接口地址**:`/publicbiz/employment/service-package/statistics`

**请求方式**:`GET`

**请求数据类型**:`application/x-www-form-urlencoded`

**响应数据类型**:`*/*`

**接口描述**:

获取服务套餐统计报表数据，用于展示不同状态下的套餐数量统计信息。统计数据基于当前登录用户的权限范围，支持按所属机构维度进行数据筛选。主要用于管理后台的数据概览和决策支持。

**请求参数**:

| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 | schema |
| --- | --- | --- | --- | --- | --- |
| agencyId | 所属机构ID，用于筛选特定机构的套餐统计数据 | query | false | integer(int64) |  |

**响应状态**:

| 状态码 | 说明 | schema                                     |
| ------ | ---- | ------------------------------------------ |
| 200    | OK   | CommonResultServicePackageStatisticsRespVO |

**响应参数**:

| 参数名称 | 参数说明 | 类型 | schema |
| --- | --- | --- | --- |
| code |  | integer(int32) | integer(int32) |
| data |  | ServicePackageStatisticsRespVO | ServicePackageStatisticsRespVO |
| &emsp;&emsp;activeCount | 已上架数量，status='active'的套餐数量 | integer(int64) |  |
| &emsp;&emsp;pendingCount | 待上架数量，status='pending'的套餐数量 | integer(int64) |  |
| &emsp;&emsp;deletedCount | 回收站数量，status='deleted'的套餐数量 | integer(int64) |  |
| &emsp;&emsp;totalCount | 总数量，所有状态套餐的总数（不包括物理删除的数据） | integer(int64) |  |
| &emsp;&emsp;agencyId | 统计范围的所属机构ID，如果为null则表示当前用户权限范围内的全部数据 | integer(int64) |  |
| &emsp;&emsp;agencyName | 统计范围的所属机构名称 | string |  |
| &emsp;&emsp;statisticsTime | 统计时间，数据生成的时间戳 | string(date-time) |  |
| msg |  | string |  |

**响应示例**:

```javascript
{
	"code": 0,
	"data": {
		"activeCount": 25,
		"pendingCount": 8,
		"deletedCount": 3,
		"totalCount": 36,
		"agencyId": 1001,
		"agencyName": "XX机构",
		"statisticsTime": "2024-01-15T10:30:00"
	},
	"msg": ""
}
```

**接口说明**:

1. **权限控制**：

   - 统计数据基于当前登录用户的所属机构权限范围
   - 如果用户属于特定机构，则只能查看该机构下的套餐统计
   - 超级管理员可以查看所有机构的统计数据

2. **参数说明**：

   - `agencyId`为可选参数，不传递时根据当前用户权限自动确定统计范围
   - 传递`agencyId`时，会筛选该机构下的套餐进行统计
   - 如果传递的`agencyId`超出当前用户权限范围，将返回权限错误

3. **统计维度**：

   - `activeCount`：已上架套餐数量，可直接对外提供服务
   - `pendingCount`：待上架套餐数量，需要审核或其他操作后才能上架
   - `deletedCount`：回收站套餐数量，已下架但未物理删除的套餐
   - `totalCount`：总数量，为上述三种状态的数量之和

4. **使用场景**：
   - 管理后台首页数据概览
   - 机构业务数据统计
   - 套餐管理决策支持
   - 运营数据分析报表
