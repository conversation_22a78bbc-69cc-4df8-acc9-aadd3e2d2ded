<!--
  页面名称：个人培训订单审批弹窗
  功能描述：支持审批通过和审批拒绝操作，调用相应的API接口
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="订单审批"
    width="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="approval-container">
      <!-- 审批表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="审批结果*" prop="approvalResult">
          <el-radio-group v-model="form.approvalResult" @change="onApprovalResultChange">
            <el-radio value="approve">
              <el-icon color="#67c23a" style="margin-right: 4px"><Check /></el-icon>
              审批通过
            </el-radio>
            <el-radio value="reject">
              <el-icon color="#f56c6c" style="margin-right: 4px"><Close /></el-icon>
              审批驳回
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="驳回原因*" prop="rejectReason" v-if="form.approvalResult === 'reject'">
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回原因..."
          />
        </el-form-item>

        <el-form-item label="审批意见" prop="comments">
          <el-input
            v-model="form.comments"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见 (可选)..."
          />
        </el-form-item>

        <!-- 订单信息展示 -->
        <el-form-item label="订单信息">
          <div class="order-info">
            <div class="info-item">
              <span class="info-label">订单号：</span>
              <span class="info-value">{{ getOrderNumber() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">学员姓名：</span>
              <span class="info-value">{{ getStudentName() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">课程名称：</span>
              <span class="info-value">{{ getCourseName() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">订单金额：</span>
              <span class="info-value amount">¥{{ formatAmount(getOrderAmount()) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前状态：</span>
              <span class="info-value">
                <el-tag :type="getOrderStatusType(orderData?.orderStatus || '')">
                  {{ getOrderStatusTextLocal(orderData?.orderStatus || '') }}
                </el-tag>
              </span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.approvalResult"
        >
          {{ form.approvalResult === 'approve' ? '审批通过' : '审批驳回' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import {
  IndividualTrainingOrderApi,
  getOrderStatusText,
  getOrderStatusTagType
} from '@/api/OrderCenter/IndividualtrainingOrder'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  approvalResult: '',
  rejectReason: '',
  comments: '',
  // 新增字段用于新API
  approvalType: 'training_approval',
  approvalLevel: 1,
  approverIds: [1], // 临时默认值，实际应该从用户信息获取
  operatorName: '当前用户' // 临时默认值
})

// 表单验证规则
const rules: FormRules = {
  approvalResult: [{ required: true, message: '请选择审批结果', trigger: 'change' }],
  rejectReason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const onApprovalResultChange = () => {
  if (form.approvalResult === 'approve') {
    form.rejectReason = ''
  }
}

const getOrderNumber = () => {
  return props.orderData?.orderNumber || props.orderData?.orderNo || '-'
}

const getStudentName = () => {
  return props.orderData?.studentName || '-'
}

const getCourseName = () => {
  return props.orderData?.courseName || '-'
}

const getOrderAmount = () => {
  return props.orderData?.orderAmount || 0
}

const formatAmount = (amount: number) => {
  return amount?.toLocaleString() || '0'
}

const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    executing: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusTextLocal = (status: string) => {
  return getOrderStatusText(status) || '未知'
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  form.approvalResult = ''
  form.rejectReason = ''
  form.comments = ''
  // 重置新增字段
  form.approvalType = 'training_approval'
  form.approvalLevel = 1
  form.approverIds = [1]
  form.operatorName = '当前用户'
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 调用新的审批API
    try {
      const { IndividualTrainingOrderApi } = await import(
        '@/api/OrderCenter/IndividualtrainingOrder'
      )

      const baseApprovalData = {
        orderId: props.orderData?.id || props.orderData?.orderId,
        approvalType: form.approvalType,
        approvalLevel: form.approvalLevel,
        approverIds: form.approverIds,
        operatorName: form.operatorName
      }

      if (form.approvalResult === 'approve') {
        // 审批通过
        await IndividualTrainingOrderApi.approve({
          ...baseApprovalData,
          approvalResult: 'approved',
          approvalOpinion: form.comments
        })
        ElMessage.success('审批通过成功')
      } else if (form.approvalResult === 'reject') {
        // 审批拒绝
        await IndividualTrainingOrderApi.reject({
          ...baseApprovalData,
          approvalResult: 'rejected',
          approvalOpinion: form.comments,
          rejectReason: form.rejectReason
        })
        ElMessage.success('审批拒绝成功')
      }

      // 发送成功事件
      emit('success', {
        approvalResult: form.approvalResult,
        approvalOpinion: form.approvalResult === 'reject' ? form.rejectReason : form.comments,
        orderData: props.orderData
      })

      visible.value = false
      resetForm()
    } catch (apiError) {
      console.error('审批API调用失败:', apiError)
      ElMessage.error('审批操作失败，请重试')
    }
  } catch (error) {
    console.error('审批操作失败:', error)
    ElMessage.error('审批操作失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.approval-container {
  .order-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 16px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        color: #666;
        font-size: 14px;
        min-width: 80px;
        flex-shrink: 0;
      }

      .info-value {
        color: #333;
        font-size: 14px;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: 600;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
