<!--
  页面名称：转派工单
  功能描述：转派工单给其他处理人，支持选择处理人、设置紧急度、填写转派原因和备注
-->
<template>
  <el-dialog
    v-model="visible"
    title="转派工单"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 工单信息提示 -->
    <div class="order-info">
      <el-text type="info"> 您正在转派工单: {{ taskInfo.taskNo }} </el-text>
    </div>

    <!-- 转派表单 -->
    <el-form ref="formRef" :model="form" :rules="rules" class="transfer-form">
      <!-- 转派给 -->
      <el-form-item label="转派给" required>
        <el-tree-select
          v-model="selectedDeptId"
          :data="deptTree"
          :props="deptTreeProps"
          check-strictly
          default-expand-all
          placeholder="请选择部门"
          style="width: 100%"
          @change="onDeptChange"
          @node-click="onDeptNodeClick"
          @visible-change="onDeptVisibleChange"
        />
        <el-select 
          v-model="selectedAssignee" 
          placeholder="请选择处理人" 
          style="width: 100%" 
          clearable
          :disabled="!selectedDeptId"
          :loading="loadingUsers"
          @change="onAssigneeChange"
        >
          <el-option
            v-for="item in assigneeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
          <el-option v-if="assigneeOptions.length === 0 && selectedDeptId" value="" disabled>
            该部门下暂无可用处理人
          </el-option>
        </el-select>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          <span v-if="!selectedDeptId">请先选择部门，再选择具体的处理人</span>
          <span v-else-if="assigneeOptions.length === 0">该部门下暂无可用处理人，请选择其他部门</span>
          <span v-else>已加载 {{ assigneeOptions.length }} 个处理人，请选择具体的处理人</span>
        </div>
      </el-form-item>


      <!-- 紧急度 -->
      <el-form-item label="紧急度" prop="urgency" required>
        <el-select v-model="form.urgency" placeholder="请选择紧急度" style="width: 100%" clearable>
          <el-option
            v-for="item in urgencyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          紧急度将影响工单的处理优先级，请根据实际情况选择
        </div>
      </el-form-item>

      <!-- 转派原因 -->
      <el-form-item label="转派原因" prop="reason" required>
        <el-select v-model="form.reason" placeholder="请选择转派原因" style="width: 100%" clearable>
          <el-option
            v-for="item in reasonOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 转派备注 -->
      <el-form-item label="转派备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="4"
          placeholder="请详细说明转派的具体原因、工单背景、需要注意的事项等..."
          maxlength="500"
          show-word-limit
        />
        <div class="form-tip"> 建议填写详细的交接信息，便于接收人快速了解情况 </div>
      </el-form-item>
    </el-form>
    
    <!-- 当前选择显示 -->
    <div v-if="currentSelection" style="margin: 12px 0; color: #888; font-size: 13px">
      当前选择：{{ currentSelection }}
    </div>

    <!-- 转派信息预览 -->
    <div class="transfer-preview">
      <div class="preview-panel">
        <div class="preview-header">
          <el-icon class="preview-icon"><InfoFilled /></el-icon>
          <span class="preview-title">转派信息预览</span>
        </div>
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">工单号:</span>
            <span class="preview-value">{{ taskInfo.taskNo }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">部门:</span>
            <span class="preview-value">{{ selectedDeptName || '-' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">转派给:</span>
            <span class="preview-value">{{ selectedAssigneeInfo?.nickname || '-' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">紧急度:</span>
            <span class="preview-value">{{ getUrgencyLabel(form.urgency) || '-' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">转派原因:</span>
            <span class="preview-value">{{ getReasonLabel(form.reason) || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="loading"
          :disabled="!selectedDeptId || !selectedAssignee"
        > 
          确认转派 
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { transferWorkOrder } from '@/api/mall/employment/workOrder'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { handleTree } from '@/utils/tree'


/** 对话框显示状态 */
const visible = ref(false)
/** 加载状态 */
const loading = ref(false)
/** 用户列表加载状态 */
const loadingUsers = ref(false)
/** 表单引用 */
const formRef = ref()

/** 工单信息 */
const taskInfo = ref({
  taskNo: '',
  id: ''
})

/** 表单数据 */
const form = reactive({
  urgency: '',
  reason: '',
  notes: ''
})

// 部门树数据和选择状态
const deptTree = ref<any[]>([])
const selectedDeptId = ref('')
const selectedDeptName = ref('')
const assigneeOptions = ref<
  { label: string; value: number; userId: number; nickname: string; username: string }[]
>([])
const selectedAssignee = ref<number | ''>('')
const selectedAssigneeInfo = ref<{ nickname: string; username: string } | null>(null)

/** 当前选择显示 */
const currentSelection = computed(() => {
  if (!selectedDeptName.value || !selectedAssignee.value || !selectedAssigneeInfo.value) return ''
  return `${selectedDeptName.value} - ${selectedAssigneeInfo.value.nickname} (${selectedAssigneeInfo.value.username})`
})

/** 表单校验规则 */
const rules = {
  urgency: [{ required: true, message: '请选择紧急度', trigger: 'change' }],
  reason: [{ required: true, message: '请选择转派原因', trigger: 'change' }]
}



/** 紧急度选项 */
const urgencyOptions = ref([
  { label: '紧急', value: 'urgent' },
  { label: '高', value: 'high' },
  { label: '中', value: 'medium' },
  { label: '低', value: 'low' }
])

/** 转派原因选项 */
const reasonOptions = ref<{ label: string; value: string }[]>([])

/** 部门树默认配置 */
const deptTreeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

/** 部门选择变化 */
const onDeptChange = async (value: any, node: any) => {
  console.log('部门选择变化 - 值:', value, '节点:', node)
  console.log('部门树数据:', deptTree.value)
  
  // 重置处理人相关数据
  selectedAssignee.value = ''
  selectedAssigneeInfo.value = null
  assigneeOptions.value = []
  
  if (!value) {
    selectedDeptId.value = ''
    selectedDeptName.value = ''
    return
  }
  
  // 如果node参数为空，尝试从部门树中查找对应的节点信息
  let deptNode = node
  if (!deptNode && deptTree.value.length > 0) {
    deptNode = findDeptNode(deptTree.value, value)
    console.log('从部门树中查找的节点:', deptNode)
  }
  
  selectedDeptId.value = value
  selectedDeptName.value = deptNode?.name || deptNode?.label || '未知部门'

  // 根据选择的部门获取处理人列表
  await loadUsersByDept(value)
}

/** 从部门树中查找指定ID的节点 */
const findDeptNode = (nodes: any[], deptId: string): any => {
  for (const node of nodes) {
    if (node.id === deptId || node.value === deptId) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findDeptNode(node.children, deptId)
      if (found) return found
    }
  }
  return null
}

/** 部门树节点点击事件 */
const onDeptNodeClick = async (node: any) => {
  console.log('部门树节点点击:', node)
  if (node && node.id) {
    // 当用户点击部门节点时，直接触发部门选择变化
    await onDeptChange(node.id, node)
  }
}

/** 部门树可见性变化 */
const onDeptVisibleChange = (visible: boolean) => {
  if (visible) {
    // 当树组件显示时，尝试触发一次部门选择变化，确保部门名称正确显示
    // 如果此时selectedDeptId为空，则不会触发onDeptChange，但会触发onDeptNodeClick
    // 如果此时selectedDeptId不为空，则直接触发onDeptChange
    if (selectedDeptId.value) {
      onDeptChange(selectedDeptId.value, findDeptNode(deptTree.value, selectedDeptId.value))
    }
  }
}

/** 监听部门ID变化，自动更新部门名称 */
watch(selectedDeptId, (newDeptId) => {
  if (newDeptId && deptTree.value.length > 0) {
    const deptNode = findDeptNode(deptTree.value, newDeptId)
    if (deptNode && !selectedDeptName.value) {
      selectedDeptName.value = deptNode.name || deptNode.label || '未知部门'
      console.log('通过watch更新部门名称:', selectedDeptName.value)
    }
  }
})

/** 根据部门加载用户列表 */
const loadUsersByDept = async (deptId: string) => {
  console.log('开始加载部门用户，部门ID:', deptId)
  
  if (!deptId) {
    assigneeOptions.value = []
    return
  }

  try {
    // 显示加载状态
    loadingUsers.value = true
    
    // 调用用户API，根据部门ID查询该部门的用户列表
    const queryParams: any = {
      pageNo: 1,
      pageSize: 100, // 获取足够多的用户
      deptId: deptId,
      status: 0 // 只获取正常状态的用户
    }
    console.log('查询参数:', queryParams)
    
    const data = await UserApi.getUserPage(queryParams)
    console.log('API返回数据:', data)

    // 关闭加载提示
    loadingUsers.value = false

    // 验证返回数据
    if (!data || !data.list || !Array.isArray(data.list)) {
      console.warn('API返回数据格式异常:', data)
      ElMessage.warning('获取处理人列表失败，数据格式异常')
      assigneeOptions.value = []
      return
    }

    // 过滤有效的用户数据
    const validUsers = data.list.filter((user: any) => 
      user && user.id && user.nickname && user.username
    )

    if (validUsers.length === 0) {
      ElMessage.info('该部门下暂无可用处理人')
      assigneeOptions.value = []
      return
    }

    // 将用户数据转换为选项格式
    assigneeOptions.value = validUsers.map((user: any) => ({
      label: `${user.nickname} (${user.username})`,
      value: user.id, // 使用用户ID作为value
      userId: user.id,
      nickname: user.nickname,
      username: user.username
    }))
    
    console.log('处理后的用户选项数量:', assigneeOptions.value.length)
    
  } catch (error: any) {
    console.error('获取部门用户失败:', error)
    
    // 根据错误类型显示不同的提示信息
    let errorMessage = '获取处理人列表失败'
    if (error.response?.status === 404) {
      errorMessage = '部门不存在或已被删除'
    } else if (error.response?.status === 403) {
      errorMessage = '没有权限查看该部门用户'
    } else if (error.message?.includes('timeout')) {
      errorMessage = '请求超时，请重试'
    } else if (error.message?.includes('Network Error')) {
      errorMessage = '网络连接失败，请检查网络'
    }
    
    ElMessage.error(errorMessage)
    assigneeOptions.value = []
  }
}

/** 处理人选择变化 */
const onAssigneeChange = (value: number) => {
  console.log('处理人选择变化:', value)
  
  if (!value) {
    selectedAssigneeInfo.value = null
    return
  }
  
  const selectedOption = assigneeOptions.value.find((option) => option.value === value)
  if (selectedOption) {
    selectedAssigneeInfo.value = {
      nickname: selectedOption.nickname,
      username: selectedOption.username
    }
    console.log('选中的处理人信息:', selectedAssigneeInfo.value)
  } else {
    selectedAssigneeInfo.value = null
    console.warn('未找到选中的处理人选项:', value)
  }
}
/** 根据value获取转派原因的label */
const getReasonLabel = (value: string) => {
  if (!value) return ''
  const option = reasonOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/** 根据value获取紧急度的label */
const getUrgencyLabel = (value: string) => {
  if (!value) return ''
  const option = urgencyOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/** 加载转派原因选项 */
const loadReasonOptions = async () => {
  const res = await getDictDataPage({ dictType: 'reassignment_reason', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  reasonOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

/** 加载部门树 */
const loadDeptTree = async () => {
  try {
    loading.value = true
    console.log('开始加载部门树数据...')
    
    const data = await DeptApi.getSimpleDeptList()
    console.log('API返回的原始部门数据:', data)
    
    deptTree.value = handleTree(data)
    console.log('处理后的部门树数据:', deptTree.value)
    
    // 验证部门树数据结构
    if (deptTree.value && deptTree.value.length > 0) {
      console.log('第一个部门节点示例:', deptTree.value[0])
      console.log('部门树节点数量:', deptTree.value.length)
    } else {
      console.warn('部门树数据为空或格式异常')
    }
    
  } catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门数据失败')
  } finally {
    loading.value = false
  }
}

/** 打开转派对话框 */
const open = async (task: any) => {
  visible.value = true
  taskInfo.value = {
    taskNo: task.taskNo || task.id || '',
    id: task.id || ''
  }

  // 重置表单和状态
  await resetForm()
  
  // 加载转派原因选项
  await loadReasonOptions()
  // 加载部门树
  await loadDeptTree()
}

/** 重置表单和状态 */
const resetForm = async () => {
  // 重置选择状态
  selectedAssignee.value = ''
  selectedDeptId.value = ''
  selectedDeptName.value = ''
  selectedAssigneeInfo.value = null
  assigneeOptions.value = []
  
  // 重置表单数据
  form.urgency = ''
  form.reason = ''
  form.notes = ''
  
  // 重置加载状态
  loadingUsers.value = false
  
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

/** 关闭对话框 */
const handleClose = () => {
  visible.value = false
  resetForm()
}

/** 取消操作 */
const handleCancel = () => {
  handleClose()
}

/** 确认转派 */
const handleConfirm = async () => {
  if (!formRef.value) return

  // 手动验证部门和处理人
  if (!selectedDeptId.value) {
    ElMessage.warning('请选择部门')
    return
  }
  if (!selectedAssignee.value) {
    ElMessage.warning('请选择处理人')
    return
  }

  try {
    // 校验表单
    await formRef.value.validate()

    loading.value = true

    // 调用转派工单接口
    const transferData = {
      workOrderNo: taskInfo.value.taskNo,
      deptId: selectedDeptId.value,
      newAssigneeId: selectedAssignee.value,
      newAssigneeName: selectedAssigneeInfo.value?.nickname || '',
      priority: form.urgency, // 紧急度
      transferReason: form.reason, // 转派原因
      transferRemark: form.notes // 转派备注
    }

    const res = await transferWorkOrder(transferData)

    // 显示成功消息
    const urgencyText = (() => {
      switch (form.urgency) {
        case 'urgent': return '紧急优先级'
        case 'high': return '高优先级'
        case 'medium': return '中等优先级'
        case 'low': return '低优先级'
        default: return '未知优先级'
      }
    })()
    const deptName = selectedDeptName.value
    const assigneeName = selectedAssigneeInfo.value ? selectedAssigneeInfo.value.nickname : ''
    
    ElMessage.success(
      `工单转派成功！\n\n工单号：${taskInfo.value.taskNo}\n部门：${deptName}\n转派给：${assigneeName}\n紧急度：${urgencyText}\n转派原因：${form.reason}\n\n系统将自动通知接收人处理该工单。`
    )

    // 触发成功事件
    emit('success', {
      taskId: taskInfo.value.id,
      deptId: selectedDeptId.value,
      deptName: deptName,
      assignee: selectedAssignee.value,
      assigneeName: assigneeName,
      urgency: form.urgency,
      priority: form.urgency,
      reason: form.reason,
      transferReason: form.reason,
      notes: form.notes,
      transferRemark: form.notes
    })

    handleClose()
  } catch (error: any) {
    console.error('转派失败:', error)
    ElMessage.error('转派失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 定义事件 */
const emit = defineEmits<{
  success: [
    data: {
      taskId: string
      deptId: string
      deptName: string
      assignee: number
      assigneeName: string
      urgency: string
      priority: string
      reason: string
      transferReason: string
      notes: string
      transferRemark: string
    }
  ]
}>()

/** 暴露方法给父组件 */
defineExpose({
  open
})
</script>

<style scoped lang="scss">
.order-info {
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.transfer-form {
  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    gap: 4px;

    .el-icon {
      font-size: 14px;
    }
  }
  
  /* 部门选择样式 */
  .el-tree-select {
    margin-bottom: 12px;
  }
  
  /* 处理人选择样式 */
  .el-select {
    margin-top: 8px;
  }
  
  /* 表单项间距 */
  .el-form-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* 禁用状态样式 */
  .el-select.is-disabled {
    .el-input__wrapper {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

/* 转派信息预览样式 */
.transfer-preview {
  margin-top: 20px;
}

.preview-panel {
  background: #fff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .preview-icon {
    color: #409eff;
    font-size: 16px;
    margin-right: 8px;
  }

  .preview-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.preview-content {
  .preview-item {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .preview-label {
      width: 80px;
      color: #606266;
      font-size: 13px;
    }

    .preview-value {
      color: #303133;
      font-size: 13px;
      font-weight: 500;
    }
  }
}
</style>
