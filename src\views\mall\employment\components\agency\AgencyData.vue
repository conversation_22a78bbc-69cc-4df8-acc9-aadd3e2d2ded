<!--
  页面名称：机构业务数据
  功能描述：展示机构的业务指标、图表等数据
  接口绑定：已绑定 /publicbiz/agency/statistics 接口
  特殊说明：面试成功率和期间我方收入显示为"--"，不绑定接口数据
-->
<template>
  <div class="agency-data">
    <!-- 数据筛选 -->
    <div class="data-filter">
      <div class="filter-buttons">
        <el-button
          v-for="period in dataPeriods"
          :key="period.value"
          :type="currentPeriod === period.value ? 'primary' : ''"
          size="small"
          @click="changePeriod(period.value)"
          :disabled="loading"
        >
          {{ period.label }}
        </el-button>
      </div>
      <div class="filter-right">
        <span v-if="loading" class="loading-text">
          <i class="el-icon-loading"></i> 数据加载中...
        </span>
        <span v-else-if="error" class="error-text">
          <i class="el-icon-warning"></i> {{ error }}
          <el-button type="text" size="small" @click="fetchBusinessData" class="retry-btn">
            重试
          </el-button>
        </span>
        <span v-else class="update-time">数据更新于: {{ formatDateTime(updateTime) }}</span>
      </div>
    </div>

    <!-- 核心业务指标 -->
    <div class="kpi-section">
      <div class="section-title">核心业务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-file-signature"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.serviceOrderCount || 0 }}</div>
            <div class="kpi-label">服务订单数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-handshake"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">--</div>
            <div class="kpi-label">面试成功率</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-star"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.agencyRating || 0 }}%</div>
            <div class="kpi-label">机构评分</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.complaintRate || 0 }}%</div>
            <div class="kpi-label">客户投诉率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阿姨相关指标 -->
    <div class="kpi-section">
      <div class="section-title">阿姨相关指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.totalPractitioners || 0 }}</div>
            <div class="kpi-label">在职阿姨总数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-plus"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.newPractitioners || 0 }}</div>
            <div class="kpi-label">期间新增阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-slash"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.flowPractitioners || 0 }}</div>
            <div class="kpi-label">期间流失阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.activeOrderPractitioners || 0 }}</div>
            <div class="kpi-label">期间上单阿姨数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 财务指标 -->
    <div class="kpi-section">
      <div class="section-title">财务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.totalOrderAmount) }}</div>
            <div class="kpi-label">期间订单总额</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">--</div>
            <div class="kpi-label">期间我方收入</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-money-check-alt"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.settledAmount) }}</div>
            <div class="kpi-label">期间已结算</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-hourglass-half"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.unsettledAmount) }}</div>
            <div class="kpi-label">期间待结算</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div class="chart-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="ordersChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务类型分布 (近30天)</h3>
          </div>
          <div class="chart-body">
            <div ref="categoryChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务质量趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="qualityChart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { getAgencyStatistics, type AgencyStatisticsVO, getAgencyTrend, type AgencyTrendVO } from '@/api/mall/employment/agency'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 数据周期选项 */
const dataPeriods = [
  { label: '近30天', value: '30' },
  { label: '近90天', value: '90' },
  { label: '本年度', value: 'year' },
  { label: '全部', value: 'all' }
]

/** 当前选中的周期 */
const currentPeriod = ref('30')

/** 更新时间 */
const updateTime = ref(new Date())

/** 加载状态 */
const loading = ref(false)

/** 错误状态 */
const error = ref<string | null>(null)

/** 业务数据 */
const businessData = reactive<Partial<AgencyStatisticsVO>>({
  serviceOrderCount: 0,
  interviewSuccessRate: 0,
  agencyRating: 0,
  complaintRate: 0,
  totalPractitioners: 0,
  newPractitioners: 0,
  flowPractitioners: 0,
  activeOrderPractitioners: 0,
  totalOrderAmount: 0,
  ourIncome: 0,
  settledAmount: 0,
  unsettledAmount: 0
})

/** 趋势数据（用于图表） */
const trendData = reactive<AgencyTrendVO>({
  orderTrends: [],
  serviceCategories: [],
  serviceQualities: []
})

/** 将 YYYY-MM 转为 MM月，仅显示月份 */
const formatMonthLabel = (value: string): string => {
  if (!value) return ''
  if (value.includes('月')) return value
  let month = value
  if (value.includes('-')) {
    const parts = value.split('-')
    month = parts[parts.length - 1] || value
  }
  month = month.replace(/^0+/, '')
  if (!month) month = '0'
  return `${month}月`
}

/** 图表引用 */
const ordersChart = ref()
const categoryChart = ref()
const qualityChart = ref()

/** 图表实例 */
let ordersChartInstance: echarts.ECharts | null = null
let categoryChartInstance: echarts.ECharts | null = null
let qualityChartInstance: echarts.ECharts | null = null

/** 切换数据周期 */
const changePeriod = (period: string) => {
  currentPeriod.value = period
  fetchBusinessData()
}

/** 更新图表数据 */
const updateChartData = () => {
  // 订单趋势
  if (ordersChartInstance) {
    const x = trendData.orderTrends.map((i) => formatMonthLabel(i.month))
    const y = trendData.orderTrends.map((i) => i.count)
    ordersChartInstance.setOption({
      xAxis: { type: 'category', data: x },
      series: [{ data: y }]
    })
  }

  // 服务类型占比（饼图）
  if (categoryChartInstance) {
    const pie = trendData.serviceCategories.map((i) => ({ value: i.percentage, name: i.serviceType }))
    categoryChartInstance.setOption({
      series: [{ type: 'pie', radius: '50%', data: pie }]
    })
  }

  // 服务质量趋势
  if (qualityChartInstance) {
    const x = trendData.serviceQualities.map((i) => formatMonthLabel(i.month))
    const y = trendData.serviceQualities.map((i) => i.score)
    qualityChartInstance.setOption({
      xAxis: { type: 'category', data: x },
      yAxis: { type: 'value', min: 0, max: 5, interval: 1 },
      series: [{ data: y }]
    })
  }
}

/** 根据周期获取订单数据 */
const getOrderDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [65, 60, 80, 78, 55, 95]
    case '90':
      return [45, 52, 68, 75, 82, 88]
    case 'year':
      return [120, 135, 150, 165, 180, 195]
    case 'all':
      return [200, 220, 240, 260, 280, 300]
    default:
      return [65, 60, 80, 78, 55, 95]
  }
}

/** 根据周期获取服务类型分布数据 */
const getCategoryDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [
        { value: 35, name: '月嫂' },
        { value: 25, name: '育儿嫂' },
        { value: 20, name: '保洁' },
        { value: 15, name: '护工' },
        { value: 5, name: '其他' }
      ]
    case '90':
      return [
        { value: 40, name: '月嫂' },
        { value: 30, name: '育儿嫂' },
        { value: 18, name: '保洁' },
        { value: 10, name: '护工' },
        { value: 2, name: '其他' }
      ]
    case 'year':
      return [
        { value: 45, name: '月嫂' },
        { value: 35, name: '育儿嫂' },
        { value: 15, name: '保洁' },
        { value: 8, name: '护工' },
        { value: 2, name: '其他' }
      ]
    case 'all':
      return [
        { value: 50, name: '月嫂' },
        { value: 40, name: '育儿嫂' },
        { value: 12, name: '保洁' },
        { value: 5, name: '护工' },
        { value: 1, name: '其他' }
      ]
    default:
      return [
        { value: 35, name: '月嫂' },
        { value: 25, name: '育儿嫂' },
        { value: 20, name: '保洁' },
        { value: 15, name: '护工' },
        { value: 5, name: '其他' }
      ]
  }
}

/** 根据周期获取服务质量数据 */
const getQualityDataByPeriod = () => {
  switch (currentPeriod.value) {
    case '30':
      return [98.2, 98.5, 99.2, 98.8, 98.5, 99.5]
    case '90':
      return [97.5, 97.8, 98.2, 98.5, 98.8, 99.0]
    case 'year':
      return [96.8, 97.2, 97.8, 98.2, 98.5, 98.8]
    case 'all':
      return [95.5, 96.2, 96.8, 97.2, 97.5, 97.8]
    default:
      return [98.2, 98.5, 99.2, 98.8, 98.5, 99.5]
  }
}

/** 获取业务数据（KPI） */
const fetchBusinessData = async () => {
  try {
    // 检查是否有机构ID
    if (!props.agency?.id) {
      console.warn('机构ID不存在，无法获取业务数据')
      return
    }

    loading.value = true
    error.value = null
    console.log('获取业务数据，周期:', currentPeriod.value)

    // 调用真实API接口（统计KPI）
    const response = await getAgencyStatistics({
      agencyId: props.agency.id,
      rangeType: currentPeriod.value as '30' | '90' | 'year' | 'all'
    })
    if (response) {
      // 更新业务数据
      Object.assign(businessData, response)

      // 更新时间
      updateTime.value = new Date()
    }
  } catch (err: any) {
    console.error('获取业务数据失败:', err)
    error.value = err?.message || '获取业务数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

/** 获取趋势数据（用于图表） */
const fetchTrendData = async () => {
  try {
    if (!props.agency?.id) return
    const res = await getAgencyTrend(props.agency.id)
    if (res) {
      trendData.orderTrends = res.orderTrends || []
      trendData.serviceCategories = res.serviceCategories || []
      trendData.serviceQualities = res.serviceQualities || []
      updateChartData()
    }
  } catch (err) {
    console.error('获取趋势数据失败:', err)
  }
}

/** 初始化订单趋势图表 */
const initOrdersChart = () => {
  if (!ordersChart.value) return

  ordersChartInstance = echarts.init(ordersChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [15, 22, 18, 25, 30, 28],
        type: 'line',
        smooth: true
      }
    ]
  }
  ordersChartInstance.setOption(option)
}

/** 初始化服务类型分布图表 */
const initCategoryChart = () => {
  if (!categoryChart.value) return

  categoryChartInstance = echarts.init(categoryChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '月嫂' },
          { value: 25, name: '育儿嫂' },
          { value: 20, name: '保洁' },
          { value: 15, name: '护工' },
          { value: 5, name: '其他' }
        ]
      }
    ]
  }
  categoryChartInstance.setOption(option)
}

/** 初始化服务质量趋势图表 */
const initQualityChart = () => {
  if (!qualityChart.value) return

  qualityChartInstance = echarts.init(qualityChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 5,
      interval: 1
    },
    series: [
      {
        data: [0, 0, 0, 0, 0, 0],
        type: 'line',
        smooth: true
      }
    ]
  }
  qualityChartInstance.setOption(option)
}

/** 格式化金额 */
const formatMoney = (amount: number | undefined) => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString()}`
}

/** 格式化日期时间 */
const formatDateTime = (date: Date) => {
  return date.toLocaleString()
}

/** 监听窗口大小变化 */
const handleResize = () => {
  ordersChartInstance?.resize()
  categoryChartInstance?.resize()
  qualityChartInstance?.resize()
}

onMounted(() => {
  // 只有在有机构数据时才获取业务数据
  if (props.agency?.id) {
    fetchBusinessData()
  }

  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    try {
      initOrdersChart()
      initCategoryChart()
      initQualityChart()
      // 初始化后拉取趋势数据填充图表
      fetchTrendData()
    } catch (err) {
      console.error('初始化图表失败:', err)
    }
  }, 100)

  window.addEventListener('resize', handleResize)
})

// 监听机构变化
watch(
  () => props.agency,
  () => {
    if (props.agency) {
      fetchBusinessData()
      fetchTrendData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.agency-data {
  .data-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .filter-buttons {
    display: flex;
    gap: 8px;
  }

  .filter-right {
    display: flex;
    align-items: center;
  }

  .loading-text {
    font-size: 12px;
    color: #409eff;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .error-text {
    font-size: 12px;
    color: #f56c6c;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .retry-btn {
    margin-left: 8px;
    padding: 0;
    height: auto;
    font-size: 12px;
  }

  .update-time {
    font-size: 12px;
    color: #6c757d;
  }

  .kpi-section {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
  }

  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .kpi-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .kpi-icon {
    font-size: 24px;
    color: #3498db;
    margin-right: 15px;
    flex-shrink: 0;
  }

  .kpi-info {
    .kpi-value {
      font-size: 22px;
      font-weight: 700;
      color: #343a40;
      display: block;
    }

    .kpi-label {
      font-size: 13px;
      color: #6c757d;
    }
  }

  .chart-section {
    margin-top: 30px;
  }

  .chart-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .chart-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .chart-header {
    padding: 12px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
  }

  .chart-body {
    padding: 15px;
  }

  .chart-container {
    width: 100%;
    height: 200px;
  }
}
</style>
