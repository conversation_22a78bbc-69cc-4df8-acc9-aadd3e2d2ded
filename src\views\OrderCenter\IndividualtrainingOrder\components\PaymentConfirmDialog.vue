<!--
  页面名称：个人培训订单收款确认弹窗
  功能描述：确认订单收款信息，支持收款金额、方式、日期等信息的录入
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="props.isUpdate ? '更新收款' : '确认收款'"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="payment-confirm-container">
      <!-- 订单信息展示 -->
      <div class="order-info">
        <h4>订单信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderData?.orderNumber || orderData?.orderNo || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">学员姓名：</span>
            <span class="value">{{ orderData?.studentName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">课程名称：</span>
            <span class="value">{{ orderData?.courseName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ formatAmount(orderData?.orderAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- 收款信息表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="实际收款金额*" prop="actualAmount">
          <el-input-number
            v-model="form.actualAmount"
            :min="0"
            :max="orderData?.orderAmount || 999999"
            :precision="2"
            style="width: 100%"
            placeholder="请输入实际收款金额"
          />
          <div class="field-hint"> 订单金额：¥{{ formatAmount(orderData?.orderAmount) }} </div>
        </el-form-item>

        <el-form-item label="收款方式*" prop="paymentMethod">
          <el-select v-model="form.paymentMethod" placeholder="请选择收款方式" style="width: 100%">
            <el-option
              v-for="(text, value) in paymentMethodOptions"
              :key="value"
              :label="text"
              :value="value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="收款日期*" prop="collectionDate">
          <el-date-picker
            v-model="form.collectionDate"
            type="date"
            placeholder="请选择收款日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="收款备注">
          <el-input
            v-model="form.paymentNotes"
            type="textarea"
            :rows="3"
            placeholder="请输入收款备注信息 (可选)"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ props.isUpdate ? '更新收款' : '确认收款' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
  isUpdate?: boolean
  existingPayment?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null,
  isUpdate: false,
  existingPayment: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: [paymentData: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  actualAmount: 0,
  paymentMethod: '',
  paymentDate: '',
  paymentNotes: '',
  // 新增字段用于新API
  operatorName: '当前用户', // 临时默认值
  transactionId: '',
  bankAccount: '',
  bankName: '',
  collectionDate: '' // 修改为 collectionDate
})

// 收款方式选项
const paymentMethodOptions = {
  bank_transfer: '银行转账',
  alipay: '支付宝',
  wechat_pay: '微信支付',
  cash: '现金',
  check: '支票',
  other: '其他',
  // 添加更多常见的收款方式
  online_payment: '在线支付',
  pos_machine: 'POS机',
  mobile_payment: '移动支付',
  credit_card: '信用卡',
  debit_card: '借记卡'
}

// 表单验证规则
const rules: FormRules = {
  actualAmount: [
    { required: true, message: '请输入实际收款金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '收款金额不能小于0', trigger: 'blur' }
  ],
  paymentMethod: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
  collectionDate: [{ required: true, message: '请选择收款日期', trigger: 'change' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const formatAmount = (amount: number | string) => {
  if (!amount) return '0.00'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  form.actualAmount = 0
  form.paymentMethod = ''
  form.paymentDate = ''
  form.paymentNotes = ''
  // 重置新增字段
  form.operatorName = '当前用户'
  form.transactionId = ''
  form.bankAccount = ''
  form.bankName = ''
  form.collectionDate = '' // 重置 collectionDate
  formRef.value?.clearValidate()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 调用新的收款确认API
    const collectionData = {
      orderId: props.orderData?.id || props.orderData?.orderId,
      orderNo: props.orderData?.orderNumber || props.orderData?.orderNo || '',
      collectionAmount: form.actualAmount,
      collectionMethod: form.paymentMethod,
      collectionDate: form.collectionDate,
      operatorName: form.operatorName,
      collectionRemark: form.paymentNotes,
      transactionId: form.transactionId || undefined,
      bankAccount: form.bankAccount || undefined,
      bankName: form.bankName || undefined
    }

    console.log('收款数据:', collectionData)
    console.log(
      '收款日期格式:',
      typeof collectionData.collectionDate,
      collectionData.collectionDate
    )

    // 调用新的API接口
    try {
      const { IndividualTrainingOrderApi } = await import(
        '@/api/OrderCenter/IndividualtrainingOrder'
      )

      if (props.isUpdate) {
        // 更新收款
        await IndividualTrainingOrderApi.updateCollection(collectionData)
        ElMessage.success('收款更新成功')
      } else {
        // 确认收款
        await IndividualTrainingOrderApi.confirmCollection(collectionData)
        ElMessage.success('收款确认成功')
      }

      // 发送确认事件（保持向后兼容）
      const paymentData = {
        orderId: props.orderData?.id,
        actualAmount: form.actualAmount,
        paymentMethod: form.paymentMethod,
        paymentDate: form.paymentDate,
        paymentNotes: form.paymentNotes,
        operator: form.operatorName,
        orderData: props.orderData
      }
      emit('confirm', paymentData)

      visible.value = false
      resetForm()
    } catch (apiError) {
      console.error('收款API调用失败:', apiError)
      ElMessage.error(props.isUpdate ? '收款更新失败，请重试' : '收款确认失败，请重试')
    }
  } catch (error) {
    console.error('收款确认失败:', error)
    ElMessage.error('收款确认失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.orderData) {
      console.log('=== PaymentConfirmDialog: 初始化表单数据 ===')
      console.log('订单数据:', props.orderData)
      console.log('是否为更新模式:', props.isUpdate)
      console.log('现有收款数据:', props.existingPayment)

      // 重置表单
      resetForm()

      if (props.isUpdate && props.existingPayment) {
        // 更新模式：回写现有收款信息
        console.log('更新模式：回写现有收款信息')

        form.actualAmount = props.existingPayment.collectionAmount || 0
        form.paymentMethod = props.existingPayment.collectionMethod || ''
        form.collectionDate =
          props.existingPayment.collectionDate || new Date().toISOString().slice(0, 10) // 只取年月日
        form.operatorName = props.existingPayment.operatorName || '当前用户'
        form.transactionId = props.existingPayment.transactionId || ''
        form.bankAccount = props.existingPayment.bankAccount || ''
        form.bankName = props.existingPayment.bankName || ''
        form.paymentNotes = props.existingPayment.collectionRemark || ''
      } else {
        // 新增模式：初始化默认值
        console.log('新增模式：初始化默认值')

        // 弹窗打开时，初始化收款金额为订单金额
        form.actualAmount = props.orderData.orderAmount || 0
        form.collectionDate = new Date().toISOString().slice(0, 10) // 只取年月日
        form.operatorName = '当前用户'

        // 回写现有收款信息（如果有）
        if (props.orderData.paymentList && props.orderData.paymentList.length > 0) {
          const existingPayment = props.orderData.paymentList[0]
          console.log('发现现有收款信息:', existingPayment)

          // 回写收款金额
          if (existingPayment.paymentAmount) {
            form.actualAmount = existingPayment.paymentAmount
          }

          // 回写收款方式
          if (existingPayment.paymentType) {
            form.paymentMethod = existingPayment.paymentType
          }

          // 回写收款日期
          if (existingPayment.paymentTime) {
            // 处理时间戳或日期字符串
            const paymentDate = new Date(existingPayment.paymentTime)
            form.collectionDate = paymentDate.toISOString().slice(0, 10) // 只取年月日
          }

          // 回写收款备注
          if (existingPayment.paymentRemark) {
            form.paymentNotes = existingPayment.paymentRemark
          }

          console.log('回写后的表单数据:', form)
        }
      }
    }
  }
)
</script>

<style scoped lang="scss">
.payment-confirm-container {
  .order-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.amount {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
