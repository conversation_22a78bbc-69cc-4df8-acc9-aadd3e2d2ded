<!--
  页面名称：个人培训订单收款管理组件
  功能描述：展示收款列表，支持收款确认、更新等操作
-->
<template>
  <div class="payment-management">
    <!-- 收款信息展示 -->
    <div class="payment-info">
      <h4>收款信息</h4>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderData?.orderNumber || orderData?.orderNo || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">学员姓名：</span>
          <span class="value">{{ orderData?.studentName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单金额：</span>
          <span class="value amount">¥{{ formatAmount(orderData?.orderAmount) }}</span>
        </div>
        <div class="info-item">
          <span class="label">已收金额：</span>
          <span class="value collected">¥{{ formatAmount(collectedAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 收款操作按钮 -->
    <div class="payment-actions">
      <el-button type="primary" @click="onAddPayment">
        <el-icon><Plus /></el-icon>
        确认收款
      </el-button>
      <el-button type="warning" @click="showUpdateDialog = true" :disabled="!hasExistingPayment">
        <el-icon><Edit /></el-icon>
        更新收款
      </el-button>
    </div>

    <!-- 收款记录列表 -->
    <div class="payment-list">
      <h4>收款记录</h4>
      <el-table :data="paymentList" style="width: 100%" v-loading="loading">
        <el-table-column prop="collectionDate" label="收款日期" width="150">
          <template #default="scope">
            {{ formatDate(scope.row.collectionDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="collectionAmount" label="收款金额" width="120">
          <template #default="scope"> ¥{{ formatAmount(scope.row.collectionAmount) }} </template>
        </el-table-column>
        <el-table-column prop="collectionMethod" label="收款方式" width="120">
          <template #default="scope">
            {{ getCollectionMethodText(scope.row.collectionMethod) }}
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人" width="100" />
        <el-table-column prop="collectionRemark" label="备注" />
        <el-table-column prop="transactionId" label="交易号" width="150" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button size="small" type="warning" @click="onUpdatePayment(scope.row)">
              更新
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 收款确认对话框 -->
    <PaymentConfirmDialog
      v-model:visible="showConfirmDialog"
      :order-data="orderData"
      :is-update="isUpdateMode"
      :existing-payment="currentPaymentData"
      @confirm="onPaymentConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Edit } from '@element-plus/icons-vue'
import PaymentConfirmDialog from './PaymentConfirmDialog.vue'
import { IndividualTrainingOrderApi } from '@/api/OrderCenter/IndividualtrainingOrder'

// Props
interface Props {
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// 响应式数据
const loading = ref(false)
const showConfirmDialog = ref(false)
const isUpdateMode = ref(false)
const currentPaymentData = ref<any>(null)
const paymentList = ref<any[]>([])

// 计算属性
const collectedAmount = computed(() => {
  return paymentList.value.reduce((sum, payment) => sum + (payment.collectionAmount || 0), 0)
})

const hasExistingPayment = computed(() => {
  return paymentList.value.length > 0
})

// 方法
const formatAmount = (amount: number | string) => {
  if (!amount) return '0.00'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const getCollectionMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat_pay: '微信支付',
    cash: '现金',
    check: '支票',
    other: '其他'
  }
  return methodMap[method] || method
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

const fetchPaymentList = async () => {
  if (!props.orderData?.id) return

  try {
    loading.value = true
    const result = await IndividualTrainingOrderApi.getPaymentList({
      orderId: props.orderData.id
    })
    paymentList.value = result.records || []
  } catch (error) {
    console.error('获取收款列表失败:', error)
    ElMessage.error('获取收款列表失败')
  } finally {
    loading.value = false
  }
}

const onPaymentConfirm = async (paymentData: any) => {
  try {
    // 收款确认成功后刷新列表
    await fetchPaymentList()
    ElMessage.success(isUpdateMode.value ? '收款更新成功' : '收款确认成功')
    // 重置更新模式
    isUpdateMode.value = false
    currentPaymentData.value = null
  } catch (error) {
    console.error('收款操作失败:', error)
  }
}

const onUpdatePayment = (payment: any) => {
  currentPaymentData.value = payment
  isUpdateMode.value = true
  showConfirmDialog.value = true
}

const onAddPayment = () => {
  isUpdateMode.value = false
  currentPaymentData.value = null
  showConfirmDialog.value = true
}

// 监听订单数据变化
watch(
  () => props.orderData,
  (newOrderData) => {
    if (newOrderData?.id) {
      fetchPaymentList()
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  if (props.orderData?.id) {
    fetchPaymentList()
  }
})
</script>

<style scoped lang="scss">
.payment-management {
  .payment-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.amount {
            color: #67c23a;
            font-weight: 600;
          }

          &.collected {
            color: #409eff;
            font-weight: 600;
          }
        }
      }
    }
  }

  .payment-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .payment-list {
    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
