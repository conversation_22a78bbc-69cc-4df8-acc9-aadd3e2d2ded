# 订单来源绑定使用示例

## 概述

订单来源现在与线索管理中的线索来源保持一致，通过 `getOrderSourceText` 方法可以动态获取线索来源数据。

## 主要方法

### 1. 异步获取订单来源文本（推荐）

```typescript
import { getOrderSourceText } from '@/api/OrderCenter/IndividualtrainingOrder'

// 异步获取订单来源显示文本
const sourceText = await getOrderSourceText('1') // 返回 '官网注册'
```

### 2. 同步获取订单来源文本（向后兼容）

```typescript
import { getOrderSourceTextSync } from '@/api/OrderCenter/IndividualtrainingOrder'

// 同步获取订单来源显示文本
const sourceText = getOrderSourceTextSync('1') // 返回 '官网注册'
```

### 3. 获取线索来源选项

```typescript
import { getLeadSourceOptions } from '@/api/OrderCenter/IndividualtrainingOrder'

// 获取所有线索来源选项
const options = await getLeadSourceOptions()
// 返回格式：
// [
//   { label: '官网注册', value: '1' },
//   { label: '市场活动', value: '2' },
//   { label: '公众号文章', value: '3' },
//   // ... 更多选项
// ]
```

## 订单来源常量

```typescript
import { ORDER_SOURCE } from '@/api/OrderCenter/IndividualtrainingOrder'

// 使用订单来源常量
const source = ORDER_SOURCE.ONLINE_MINIPROGRAM // '1'
const sourceText = await getOrderSourceText(source) // '官网注册'
```

## 在Vue组件中的使用

```vue
<template>
  <div>
    <span>订单来源：{{ orderSourceText }}</span>
    <el-select v-model="form.orderSource" placeholder="请选择订单来源">
      <el-option
        v-for="option in leadSourceOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getOrderSourceText,
  getLeadSourceOptions,
  ORDER_SOURCE
} from '@/api/OrderCenter/IndividualtrainingOrder'

const form = ref({
  orderSource: ORDER_SOURCE.ONLINE_MINIPROGRAM
})

const orderSourceText = ref('')
const leadSourceOptions = ref([])

// 获取线索来源选项
const fetchLeadSourceOptions = async () => {
  try {
    leadSourceOptions.value = await getLeadSourceOptions()
  } catch (error) {
    console.error('获取线索来源选项失败:', error)
  }
}

// 获取订单来源文本
const updateOrderSourceText = async () => {
  try {
    orderSourceText.value = await getOrderSourceText(form.value.orderSource)
  } catch (error) {
    console.error('获取订单来源文本失败:', error)
  }
}

// 监听订单来源变化
const handleOrderSourceChange = () => {
  updateOrderSourceText()
}

onMounted(() => {
  fetchLeadSourceOptions()
  updateOrderSourceText()
})
</script>
```

## 注意事项

1. **异步方法**：`getOrderSourceText` 现在是异步方法，需要使用 `await` 或 `.then()`
2. **向后兼容**：提供了 `getOrderSourceTextSync` 同步方法保持向后兼容
3. **错误处理**：如果线索管理接口调用失败，会自动降级到本地映射
4. **数据一致性**：订单来源的值现在与线索来源完全一致

## 接口地址

- 获取线索来源选项：`/publicbiz/leads/lead-source-options`
- 线索管理基础路径：`/infra/clueCenter`

