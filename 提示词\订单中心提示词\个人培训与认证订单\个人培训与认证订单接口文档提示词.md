提示词请分析以下企业培训订单相关文件：

1. 数据库表结构：`提示词\订单中心提示词\个人培训与认证订单\个人培训与认证订单数据库表结构.sql`
2. 前端页面文件：
   - 主页面：`src\views\OrderCenter\IndividualtrainingOrder\index.vue`
   - 新增高校实践订单组件：`src\views\OrderCenter\IndividualtrainingOrder\components\AddIndividualtrainingOrder.vue`
   - 查看高校实践订单组件：`src\views\OrderCenter\IndividualtrainingOrder\components\IndividualtrainingOrderView.vue`
   - 查看操作日志组件：`src/views/OrderCenter/IndividualtrainingOrder/components/OptLog.vue`

基于这些文件的内容，请为我生成一份完整的高校实践订单API接口文档，不需要新增表结构；文档需要包含：

**文档结构要求：**

1. 接口概述和基础信息（基础路径、数据格式、统一响应格式）
2. 接口必须包含一个动词指示该接口的功能，例如page/add/update/delete，不要RESTfull API风格
3. 每个接口的详细说明，包括：
   - 接口地址和请求方式
   - 功能说明
   - 请求参数表格（参数名、类型、必填、说明）
   - 响应字段表格（字段名、类型、说明）
   - 完整的请求示例（包含URL和JSON数据）
   - 完整的返回示例（JSON格式）
4. 数据字典（枚举值说明）
5. 错误码说明

**接口范围：**

1、上传附件功能沿用原来的接口(/infra/file/upload)；2、操作日志列表和审批列表共用接口；3、新建订单-插入收款流水表-插入操作日志表; 4、收款确认、收款更新、收款详情、收款列表；5、审批、审批列表、审批通过、审批拒绝；6、合同确认、合同更新、合同信息获取；7、列表分页查询、导出、根据单号获取数据；8、其他从前端代码中识别出的相关接口

## 请确保文档格式规范、内容完整，便于开发者理解和对接使用。

---

#### 1. 个人培训订单状态流转

```
草稿 → 待支付 → 已支付 → 学习中 → 待考试 → 已通过/已完成
  ↓       ↓       ↓       ↓       ↓       ↓
 draft  pending   paid   learning pending  passed/
                                _exam      completed
```

待支付 → 已支付 → 已退款 ↓ ↓ ↓ pending paid refunded ↓ 已取消 cancelled

```


```

未开始 → 待审批 → 审批中 → 审批通过/审批不通过 ↓ ↓ ↓ ↓ NOT_START WAIT RUNNING APPROVE/REJECT ↓ 已取消/已退回 CANCEL/RETURN

```
未签署 → 已签署 → 已拒绝
   ↓       ↓       ↓
unsigned  signed  rejected
```

### 2. 结算状态流转

```
待结算 → 结算中 → 已结算
   ↓       ↓       ↓
pending  processing completed
   ↓
结算失败
failed
```

### 3. 学习/考试状态流转（个人培训）

```
未开始 → 学习中 → 待考试 → 待确认 → 已通过/已完成
   ↓       ↓       ↓       ↓         ↓
not_started learning pending pending  passed/
                    _exam  _confirmation completed
```

### 4. 状态流转的触发条件

#### 4.1 自动流转

- **时间触发**：超时自动审批、自动提醒
- **条件触发**：支付完成后自动进入执行状态
- **流程触发**：审批通过后自动进入下一状态

#### 4.2 手动流转

- **审批操作**：同意、拒绝、驳回、转办、委派、加签、减签
- **状态更新**：手动修改订单状态
- **流程控制**：撤销、终止流程

### 5. 状态流转的业务规则

#### 5.1 正向流转

- 订单创建后进入草稿状态
- 提交审批后进入待审批状态
- 审批通过后进入待支付状态
- 支付完成后进入执行状态
- 执行完成后进入已完成状态

#### 6.2 异常流转

- **审批拒绝**：可重新提交审批
- **支付取消**：可重新发起支付
- **执行异常**：可暂停、延期或取消
- **流程异常**：可撤销、终止或重新发起
