<!--
  页面名称：个人培训与认证订单首页
  功能描述：展示个人培训与认证订单列表，支持搜索、分页、新增、编辑、删除、查看操作日志
-->
<template>
  <div class="individual-training-index">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.totalOrders || 0 }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.pendingOrders || 0 }}</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥{{ formatAmount(statistics.monthlyAmount) }}</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.completionRate || 0 }}%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" @submit.prevent="onSearch">
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="全部订单状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部订单状态" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="待审批" value="pending_approval" />
            <el-option label="审批中" value="approving" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="待支付" value="pending_payment" />
            <el-option label="执行中" value="executing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select
            v-model="searchForm.paymentStatus"
            placeholder="全部支付状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部支付状态" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="searchForm.orderType"
            placeholder="全部类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部类型" value="" />
            <el-option label="个人培训" value="training" />
            <el-option label="考试认证" value="certification" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索学员姓名、课程名称..."
            style="width: 300px"
            clearable
          />
        </el-form-item>
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="searchForm.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 150px"
            clearable
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="searchForm.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 150px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-buttons">
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          新建个人培训订单
        </el-button>
        <el-button type="warning" @click="onExport">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNo" label="订单号" width="150">
          <template #default="scope">
            <el-link type="primary" @click="onView(scope.row)">
              {{ scope.row.orderNo || scope.row.orderNumber || '-' }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="studentName" label="学员姓名" width="120">
          <template #default="scope">
            {{ scope.row.studentName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="courseType" label="订单类型" width="120">
          <template #default="scope">
            <el-tag :type="getOrderTypeTagType(scope.row.courseType) as any">
              {{ getOrderTypeText(scope.row.courseType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="courseName" label="课程/考试项目" width="250">
          <template #default="scope">
            {{ scope.row.courseName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="businessLine" label="业务线" width="120">
          <template #default="scope">
            <el-tooltip content="已添加到剪贴板" placement="top">
              <span
                class="copy-text"
                @click="copyToClipboard(scope.row.businessLine || scope.row.courseType || '')"
              >
                {{ scope.row.businessLine || scope.row.courseType || '-' }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="scope"> ¥{{ formatAmount(scope.row.orderAmount || 0) }} </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusTagType(scope.row.orderStatus) as any">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusTagType(scope.row.paymentStatus) as any">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="learningStatus" label="学习/考试状态" width="120">
          <template #default="scope">
            <el-tag :type="getLearningStatusTagType(scope.row.learningStatus) as any">
              {{ getLearningStatusText(scope.row.learningStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registrationTime" label="报名时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.registrationTime || scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="onView(scope.row)">查看</el-button>
            <el-button
              size="small"
              type="warning"
              @click="onEdit(scope.row)"
              :loading="loading"
              :disabled="loading"
            >
              {{ loading ? '获取中...' : '编辑' }}
            </el-button>
            <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddIndividualtrainingOrder
      v-model:visible="drawerVisible"
      :edit-data="editData"
      @success="onSuccess"
    />

    <!-- 查看详情抽屉 -->
    <IndividualtrainingOrderView
      v-model:visible="viewDrawerVisible"
      :order-data="currentOrderData"
      :order-no="currentOrderData?.orderNo"
      @edit="onEditFromView"
      @order-status-updated="onOrderStatusUpdated"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-data="currentOrderData" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import AddIndividualtrainingOrder from './components/AddIndividualtrainingOrder.vue'
import IndividualtrainingOrderView from './components/IndividualtrainingOrderView.vue'
import OptLog from './components/OptLog.vue'
import {
  IndividualTrainingOrderApi,
  getOrderTypeText,
  getOrderStatusText,
  getPaymentStatusText,
  getLearningStatusText,
  getOrderTypeTagType,
  getOrderStatusTagType,
  getPaymentStatusTagType,
  getLearningStatusTagType,
  ORDER_TYPE,
  ORDER_STATUS,
  PAYMENT_STATUS,
  LEARNING_STATUS,
  DEFAULT_PAGE_PARAMS,
  DEFAULT_SEARCH_PARAMS
} from '@/api/OrderCenter/IndividualtrainingOrder'

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref({
  orderStatus: '',
  paymentStatus: '',
  orderType: '',
  keyword: '',
  startDate: '',
  endDate: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])

/** 分页信息 */
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

/** 加载状态 */
const loading = ref(false)

/** 抽屉显示状态 */
const drawerVisible = ref(false)
const viewDrawerVisible = ref(false)
const optLogVisible = ref(false)

/** 编辑数据 */
const editData = ref<any>(null)

/** 当前订单数据 */
const currentOrderData = ref<any>(null)

/** 当前订单ID */
const currentOrderId = ref('')

/** 统计信息 */
const statistics = ref({
  totalOrders: 0,
  pendingOrders: 0,
  monthlyAmount: 0,
  completionRate: 0
})

// 方法

/** 格式化金额 */
const formatAmount = (amount: number): string => {
  if (!amount) return '0'
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

/** 格式化日期 */
const formatDate = (timestamp: string | number | Date): string => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/** 复制到剪贴板 */
const copyToClipboard = (text: string) => {
  if (!text || text === '-') {
    ElMessage.warning('没有可复制的内容')
    return
  }

  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('已复制到剪贴板')
    })
  } else {
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('已复制到剪贴板')
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    orderStatus: '',
    paymentStatus: '',
    orderType: '',
    keyword: '',
    startDate: '',
    endDate: ''
  }
  onSearch()
}

/** 获取统计信息 */
const fetchStatistics = async () => {
  try {
    const result = await IndividualTrainingOrderApi.getStatistics()
    statistics.value = result
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      orderStatus: searchForm.value.orderStatus || undefined,
      paymentStatus: searchForm.value.paymentStatus || undefined,
      orderType: searchForm.value.orderType || undefined,
      keyword: searchForm.value.keyword || undefined,
      startDate: searchForm.value.startDate || undefined,
      endDate: searchForm.value.endDate || undefined
    }

    console.log('=== 调用fetchList ===')
    console.log('查询参数:', params)

    const result = await IndividualTrainingOrderApi.getOrderPage(params)

    console.log('=== API返回结果 ===')
    console.log('完整结果:', result)
    console.log('records:', result.records)
    console.log('total:', result.total)

    // 确保数据有效性
    if (result && Array.isArray(result.records)) {
      tableData.value = result.records || []
      pagination.value.total = result.total || 0

      console.log('=== 设置到组件 ===')
      console.log('tableData.value:', tableData.value)
      console.log('pagination.value:', pagination.value)
    } else {
      console.warn('API返回的数据格式不正确:', result)
      tableData.value = []
      pagination.value.total = 0
      ElMessage.warning('数据格式不正确，请检查接口返回')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取订单列表失败')
    tableData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

/** 新增 */
const onAdd = () => {
  // 确保新增时清空所有编辑数据
  editData.value = null
  // 重置当前订单数据
  currentOrderData.value = null
  // 打开抽屉
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = async (row: any) => {
  try {
    loading.value = true
    ElMessage.info('正在获取订单详情...')

    // 调用查看详情接口获取完整数据
    const orderDetail = await IndividualTrainingOrderApi.getOrderByOrderNo(row.orderNo)

    if (orderDetail) {
      // 将详情数据回写到编辑表单
      editData.value = {
        ...orderDetail,
        // 确保关键字段正确映射
        id: orderDetail.id || orderDetail.orderId, // 优先使用 id，如果没有则使用 orderId
        orderNo: orderDetail.orderNo,
        orderType: orderDetail.orderType,
        businessLine: orderDetail.businessLine,
        businessOpportunity: orderDetail.opportunityId || orderDetail.businessOpportunity,
        associatedLead: orderDetail.leadId || orderDetail.associatedLead,
        studentName: orderDetail.studentName,
        studentPhone: orderDetail.studentPhone,
        studentEmail: orderDetail.studentEmail,
        studentOneid: orderDetail.studentOneid,
        courseName: orderDetail.courseName,
        courseType: orderDetail.courseType,
        orderSource: orderDetail.orderSource,
        orderAmount: orderDetail.orderAmount,
        orderStatus: orderDetail.orderStatus,
        paymentStatus: orderDetail.paymentStatus,
        learningStatus: orderDetail.learningStatus,
        examStatus: orderDetail.examStatus,
        registrationTime: orderDetail.createTime
          ? new Date(orderDetail.createTime).toISOString()
          : undefined,
        remark: orderDetail.remark,
        createTime: orderDetail.createTime
          ? new Date(orderDetail.createTime).toISOString()
          : undefined,
        updateTime: orderDetail.updateTime
          ? new Date(orderDetail.updateTime).toISOString()
          : undefined,
        // 新增字段映射
        projectName: orderDetail.projectName,
        projectDescription: orderDetail.projectDescription,
        startDate: orderDetail.startDate,
        endDate: orderDetail.endDate,
        paidAmount: orderDetail.paidAmount,
        refundAmount: orderDetail.refundAmount,
        managerName: orderDetail.managerName,
        managerPhone: orderDetail.managerPhone,
        contractType: orderDetail.contractType,
        contractFileUrl: orderDetail.contractFileUrl,
        settlementStatus: orderDetail.settlementStatus,
        settlementTime: orderDetail.settlementTime,
        settlementMethod: orderDetail.settlementMethod,
        approvalLevel: orderDetail.approvalLevel,
        // 合同信息映射
        contractId: orderDetail.contractInfo?.contractId,
        contractName: orderDetail.contractInfo?.contractName,
        contractNumber: orderDetail.contractInfo?.contractNumber,
        contractStartDate: orderDetail.contractInfo?.startDate
          ? `${orderDetail.contractInfo.startDate[0]}-${String(orderDetail.contractInfo.startDate[1]).padStart(2, '0')}-${String(orderDetail.contractInfo.startDate[2]).padStart(2, '0')}`
          : undefined,
        contractEndDate: orderDetail.contractInfo?.endDate
          ? `${orderDetail.contractInfo.endDate[0]}-${String(orderDetail.contractInfo.endDate[1]).padStart(2, '0')}-${String(orderDetail.contractInfo.endDate[2]).padStart(2, '0')}`
          : undefined,
        contractAmount: orderDetail.contractInfo?.amount,
        contractStatus: orderDetail.contractInfo?.status,
        contractAttachment: orderDetail.contractInfo?.attachmentPath,
        contractSigner: orderDetail.contractInfo?.signer
      }

      console.log('获取到的订单详情数据:', orderDetail)
      console.log('回写到编辑表单的数据:', editData.value)

      // 打开编辑抽屉
      drawerVisible.value = true
      ElMessage.success('订单详情获取成功')
    } else {
      ElMessage.warning('未获取到订单详情，使用列表数据')
      // 确保列表数据包含必要的ID字段
      editData.value = {
        ...row,
        id: row.id || row.orderId // 确保有ID字段
      }
      drawerVisible.value = true
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，使用列表数据')

    // 降级处理：使用列表数据，确保有ID字段
    editData.value = {
      ...row,
      id: row.id || row.orderId // 确保有ID字段
    }
    drawerVisible.value = true
  } finally {
    loading.value = false
  }
}

/** 查看 */
const onView = (row: any) => {
  currentOrderData.value = { ...row }
  viewDrawerVisible.value = true
}

/** 从查看页面编辑 */
const onEditFromView = async (data: any) => {
  try {
    loading.value = true
    ElMessage.info('正在获取订单详情...')

    // 调用查看详情接口获取完整数据
    const orderDetail = await IndividualTrainingOrderApi.getOrderByOrderNo(data.orderNo)

    if (orderDetail) {
      // 将详情数据回写到编辑表单
      editData.value = {
        ...orderDetail,
        // 确保关键字段正确映射
        id: orderDetail.id || orderDetail.orderId, // 优先使用 id，如果没有则使用 orderId
        orderNo: orderDetail.orderNo,
        orderType: orderDetail.orderType,
        businessLine: orderDetail.businessLine,
        businessOpportunity: orderDetail.opportunityId || orderDetail.businessOpportunity,
        associatedLead: orderDetail.leadId || orderDetail.associatedLead,
        studentName: orderDetail.studentName,
        studentPhone: orderDetail.studentPhone,
        studentEmail: orderDetail.studentEmail,
        studentOneid: orderDetail.studentOneid,
        courseName: orderDetail.courseName,
        courseType: orderDetail.courseType,
        orderSource: orderDetail.orderSource,
        orderAmount: orderDetail.orderAmount,
        orderStatus: orderDetail.orderStatus,
        paymentStatus: orderDetail.paymentStatus,
        learningStatus: orderDetail.learningStatus,
        examStatus: orderDetail.examStatus,
        registrationTime: orderDetail.createTime
          ? new Date(orderDetail.createTime).toISOString()
          : undefined,
        remark: orderDetail.remark,
        createTime: orderDetail.createTime
          ? new Date(orderDetail.createTime).toISOString()
          : undefined,
        updateTime: orderDetail.updateTime
          ? new Date(orderDetail.updateTime).toISOString()
          : undefined,
        // 新增字段映射
        projectName: orderDetail.projectName,
        projectDescription: orderDetail.projectDescription,
        startDate: orderDetail.startDate,
        endDate: orderDetail.endDate,
        paidAmount: orderDetail.paidAmount,
        refundAmount: orderDetail.refundAmount,
        managerName: orderDetail.managerName,
        managerPhone: orderDetail.managerPhone,
        contractType: orderDetail.contractType,
        contractFileUrl: orderDetail.contractFileUrl,
        settlementStatus: orderDetail.settlementStatus,
        settlementTime: orderDetail.settlementTime,
        settlementMethod: orderDetail.settlementMethod,
        approvalLevel: orderDetail.approvalLevel,
        // 合同信息映射
        contractId: orderDetail.contractInfo?.contractId,
        contractName: orderDetail.contractInfo?.contractName,
        contractNumber: orderDetail.contractInfo?.contractNumber,
        contractStartDate: orderDetail.contractInfo?.startDate
          ? `${orderDetail.contractInfo.startDate[0]}-${String(orderDetail.contractInfo.startDate[1]).padStart(2, '0')}-${String(orderDetail.contractInfo.startDate[2]).padStart(2, '0')}`
          : undefined,
        contractEndDate: orderDetail.contractInfo?.endDate
          ? `${orderDetail.contractInfo.endDate[0]}-${String(orderDetail.contractInfo.endDate[1]).padStart(2, '0')}-${String(orderDetail.contractInfo.endDate[2]).padStart(2, '0')}`
          : undefined,
        contractAmount: orderDetail.contractInfo?.amount,
        contractStatus: orderDetail.contractInfo?.status,
        contractAttachment: orderDetail.contractInfo?.attachmentPath,
        contractSigner: orderDetail.contractInfo?.signer
      }

      console.log('从查看页面获取到的订单详情数据:', orderDetail)
      console.log('回写到编辑表单的数据:', editData.value)

      // 关闭查看抽屉，打开编辑抽屉
      viewDrawerVisible.value = false
      drawerVisible.value = true
      ElMessage.success('订单详情获取成功')
    } else {
      ElMessage.warning('未获取到订单详情，使用查看页面数据')
      // 确保查看页面数据包含必要的ID字段
      editData.value = {
        ...data,
        id: data.id || data.orderId // 确保有ID字段
      }
      viewDrawerVisible.value = false
      drawerVisible.value = true
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败，使用查看页面数据')

    // 降级处理：使用查看页面数据，确保有ID字段
    editData.value = {
      ...data,
      id: data.id || data.orderId // 确保有ID字段
    }
    viewDrawerVisible.value = false
    drawerVisible.value = true
  } finally {
    loading.value = false
  }
}

/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await IndividualTrainingOrderApi.deleteOrder({ id: row.id })
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 操作日志 */
const onOptLog = (row: any) => {
  // 设置当前订单数据，供操作日志组件使用
  currentOrderData.value = { ...row }
  optLogVisible.value = true
}

/** 导出订单 */
const onExport = async () => {
  try {
    const params = {
      orderStatus: searchForm.value.orderStatus || undefined,
      paymentStatus: searchForm.value.paymentStatus || undefined,
      orderType: searchForm.value.orderType || undefined,
      keyword: searchForm.value.keyword || undefined,
      startDate: searchForm.value.startDate || undefined,
      endDate: searchForm.value.endDate || undefined,
      exportType: 'excel'
    }

    const result = await IndividualTrainingOrderApi.exportOrders(params)
    if (result.downloadUrl) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = result.downloadUrl
      link.download = result.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('导出成功')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

/** 成功回调 */
const onSuccess = () => {
  drawerVisible.value = false
  fetchList()
}

/** 订单状态更新回调 */
const onOrderStatusUpdated = (updatedOrder: any) => {
  const index = tableData.value.findIndex((item) => item.id === updatedOrder.id)
  if (index > -1) {
    tableData.value[index] = updatedOrder
    ElMessage.success('订单状态更新成功')
  }
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

// 生命周期
onMounted(() => {
  fetchStatistics()
  fetchList()
})

// 组件卸载前清理数据
onBeforeUnmount(() => {
  // 关闭所有弹窗
  drawerVisible.value = false
  viewDrawerVisible.value = false
  optLogVisible.value = false

  // 清空数据
  tableData.value = []
  editData.value = null
  currentOrderData.value = null
  currentOrderId.value = ''

  // 重置搜索表单
  Object.assign(searchForm.value, {
    orderStatus: '',
    paymentStatus: '',
    orderType: '',
    keyword: '',
    startDate: '',
    endDate: ''
  })

  // 重置分页
  Object.assign(pagination.value, {
    page: 1,
    size: 10,
    total: 0
  })
})
</script>

<style scoped lang="scss">
.individual-training-index {
  padding: 20px;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #409eff;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .copy-text {
      cursor: pointer;
      color: #409eff;

      &:hover {
        text-decoration: underline;
      }
    }

    .pagination-section {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
