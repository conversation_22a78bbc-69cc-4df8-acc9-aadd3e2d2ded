<!--
  页面名称：个人培训订单审批管理组件
  功能描述：整合发起审批、审批操作和审批列表功能
-->
<template>
  <div class="approval-management">
    <!-- 审批信息展示 -->
    <div class="approval-info">
      <h4>审批信息</h4>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderData?.orderNumber || orderData?.orderNo || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">学员姓名：</span>
          <span class="value">{{ orderData?.studentName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单状态：</span>
          <span class="value">
            <el-tag :type="getOrderStatusType(orderData?.orderStatus)">
              {{ getOrderStatusText(orderData?.orderStatus) }}
            </el-tag>
          </span>
        </div>
        <div class="info-item">
          <span class="label">审批级别：</span>
          <span class="value">{{ orderData?.approvalLevel || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- 审批操作按钮 -->
    <div class="approval-actions">
      <el-button type="primary" @click="showSubmitDialog = true">
        <el-icon><Plus /></el-icon>
        发起审批
      </el-button>
      <el-button type="warning" @click="showApprovalDialog = true" :disabled="!canApprove">
        <el-icon><Check /></el-icon>
        审批操作
      </el-button>
    </div>

    <!-- 审批记录列表 -->
    <div class="approval-list">
      <h4>审批记录</h4>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" @submit.prevent="onFilter">
          <el-form-item label="审批类型">
            <el-select
              v-model="filterForm.approvalType"
              placeholder="全部审批类型"
              clearable
              style="width: 150px"
            >
              <el-option label="培训审批" value="training_approval" />
              <el-option label="认证审批" value="certification_approval" />
              <el-option label="合同审批" value="contract_approval" />
              <el-option label="付款审批" value="payment_approval" />
            </el-select>
          </el-form-item>
          <el-form-item label="审批结果">
            <el-select
              v-model="filterForm.approvalResult"
              placeholder="全部审批结果"
              clearable
              style="width: 150px"
            >
              <el-option label="待审批" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="审批中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期">
            <el-date-picker
              v-model="filterForm.startDate"
              type="date"
              placeholder="选择开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 150px"
              clearable
            />
          </el-form-item>
          <el-form-item label="结束日期">
            <el-date-picker
              v-model="filterForm.endDate"
              type="date"
              placeholder="选择结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 150px"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onFilter">筛选</el-button>
            <el-button @click="onResetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="approvalList" style="width: 100%" v-loading="loading">
        <el-table-column prop="approvalNo" label="审批单号" width="150" />
        <el-table-column prop="approvalType" label="审批类型" width="120">
          <template #default="scope">
            {{ getApprovalTypeText(scope.row.approvalType) }}
          </template>
        </el-table-column>
        <el-table-column prop="approvalLevel" label="审批级别" width="100">
          <template #default="scope"> 第{{ scope.row.approvalLevel }}级 </template>
        </el-table-column>
        <el-table-column prop="approvalResult" label="审批结果" width="100">
          <template #default="scope">
            <el-tag :type="getTagType(scope.row.approvalResult)">
              {{ getStatusText(scope.row.approvalResult) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人" width="100" />
        <el-table-column prop="approvalOpinion" label="审批意见" />
        <el-table-column prop="approvalTime" label="审批时间" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.approvalTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 发起审批对话框 -->
    <SubmitApprovalDialog
      v-model:visible="showSubmitDialog"
      :order-data="orderData"
      @success="onApprovalSubmitted"
    />

    <!-- 审批操作对话框 -->
    <OrderApprovalDialog
      v-model:visible="showApprovalDialog"
      :order-data="orderData"
      @success="onApprovalOperated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Check } from '@element-plus/icons-vue'
import SubmitApprovalDialog from './SubmitApprovalDialog.vue'
import OrderApprovalDialog from './OrderApprovalDialog.vue'
import { IndividualTrainingOrderApi } from '@/api/OrderCenter/IndividualtrainingOrder'

// Props
interface Props {
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// 响应式数据
const loading = ref(false)
const showSubmitDialog = ref(false)
const showApprovalDialog = ref(false)
const approvalList = ref<any[]>([])

// 筛选表单
const filterForm = ref({
  approvalType: '',
  approvalResult: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const canApprove = computed(() => {
  // 判断是否可以审批：有审批记录且状态为待审批
  return approvalList.value.some((item) => item.approvalResult === 'pending')
})

// 方法
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    executing: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    executing: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getApprovalTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    training_approval: '培训审批',
    contract_approval: '合同审批',
    cost_approval: '费用审批',
    other_approval: '其他审批'
  }
  return typeMap[type] || '未知'
}

const getTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已驳回'
  }
  return statusMap[status] || '未知'
}

const formatTime = (timestamp: string) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const fetchApprovalList = async () => {
  if (!props.orderData?.id) return

  try {
    loading.value = true
    const params: any = {
      orderId: props.orderData.id,
      page: 1,
      size: 50
    }

    if (filterForm.value.approvalType) {
      params.approvalType = filterForm.value.approvalType
    }
    if (filterForm.value.approvalResult) {
      params.approvalResult = filterForm.value.approvalResult
    }
    if (filterForm.value.startDate) {
      params.startDate = filterForm.value.startDate
    }
    if (filterForm.value.endDate) {
      params.endDate = filterForm.value.endDate
    }

    const result = await IndividualTrainingOrderApi.getApprovalList(params)
    approvalList.value = result.records || []
  } catch (error) {
    console.error('获取审批列表失败:', error)
    ElMessage.error('获取审批列表失败')
    approvalList.value = []
  } finally {
    loading.value = false
  }
}

const onFilter = () => {
  fetchApprovalList()
}

const onResetFilter = () => {
  filterForm.value = {
    approvalType: '',
    approvalResult: '',
    startDate: '',
    endDate: ''
  }
  fetchApprovalList()
}

const onApprovalSubmitted = async (approvalData: any) => {
  try {
    // 审批提交成功后刷新列表
    await fetchApprovalList()
    ElMessage.success('审批申请提交成功')
  } catch (error) {
    console.error('刷新审批列表失败:', error)
  }
}

const onApprovalOperated = async (approvalData: any) => {
  try {
    // 审批操作成功后刷新列表
    await fetchApprovalList()
    ElMessage.success('审批操作成功')
  } catch (error) {
    console.error('刷新审批列表失败:', error)
  }
}

// 监听订单数据变化
watch(
  () => props.orderData,
  (newOrderData) => {
    if (newOrderData?.id) {
      fetchApprovalList()
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  if (props.orderData?.id) {
    fetchApprovalList()
  }
})
</script>

<style scoped lang="scss">
.approval-management {
  .approval-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;
        }
      }
    }
  }

  .approval-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .approval-list {
    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .filter-section {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
  }
}
</style>
