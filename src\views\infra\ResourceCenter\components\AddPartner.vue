<!--
  页面名称：新增合作伙伴
  功能描述：新增合作伙伴，分组表单，支持多类型字段、文件上传、评分、分组标题、两列布局、底部按钮
-->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    size="600px"
    :with-header="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      label-position="top"
      class="partner-form"
    >
      <!-- 机构基本信息 -->
      <div class="form-section">
        <div class="form-group-title">机构基本信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="机构全称" prop="name" required>
              <el-input v-model="form.name" placeholder="必填, 机构全称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="机构简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构类型" prop="type" required>
              <el-select v-model="form.type" placeholder="请选择">
                <el-option
                  v-for="item in orgTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 新增业务模块下拉框 -->
          <el-col :span="12">
            <el-form-item label="业务模块" prop="bizModule">
              <el-select v-model="form.bizModule" placeholder="请选择">
                <el-option
                  v-for="item in bizModuleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作状态" prop="status" required>
              <el-select v-model="form.status" placeholder="请选择">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人代表" prop="legalPerson">
              <el-input v-model="form.legalPerson" placeholder="法定代表人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成立日期" prop="foundationDate">
              <el-date-picker
                v-model="form.foundationDate"
                type="date"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="creditCode" required>
              <el-input v-model="form.creditCode" placeholder="统一社会信用代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册资本(万元)" prop="registeredCapital" required>
              <el-input-number
                v-model="form.registeredCapital"
                :precision="2"
                :step="0.01"
                :min="0"
                placeholder="请输入注册资本（万元）"
                class="w-1/1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册地址" prop="registerAddress">
              <el-input v-model="form.registerAddress" placeholder="注册地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经营地址" prop="businessAddress">
              <el-input v-model="form.businessAddress" placeholder="经营地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="主营业务" prop="mainBusiness">
          <el-input
            v-model="form.mainBusiness"
            type="textarea"
            :rows="2"
            placeholder="主营业务描述"
          />
        </el-form-item>
      </div>

      <!-- 合作与商业信息 -->
      <div class="form-section">
        <div class="form-group-title">合作与商业信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="主要联系人" prop="contactName">
              <el-input v-model="form.contactName" placeholder="主要联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="联系电话" />
            </el-form-item>
          </el-col>
          <!-- 省市区三级联动选择器 -->
          <el-col :span="12">
            <el-form-item label="所在地区" prop="areaId">
              <el-cascader
                v-model="form.areaId"
                :options="areaList"
                :props="defaultProps"
                class="w-1/1"
                clearable
                filterable
                placeholder="请选择省市区"
                @change="handleAreaChange"
              />
            </el-form-item>
          </el-col>
          <!-- 详细地址输入框 -->
          <el-col :span="12">
            <el-form-item label="详细地址" prop="detailAddress">
              <el-input v-model="form.detailAddress" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
          <!-- 经度输入框 -->
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="form.longitude"
                :precision="7"
                :step="0.0000001"
                :min="-180"
                :max="180"
                placeholder="请输入经度"
                class="w-1/1"
              />
            </el-form-item>
          </el-col>
          <!-- 纬度输入框 -->
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="form.latitude"
                :precision="7"
                :step="0.0000001"
                :min="-90"
                :max="90"
                placeholder="请输入纬度"
                class="w-1/1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前评级" prop="rating">
              <el-rate v-model="form.rating" :max="5" show-score />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作模式" prop="cooperationMode">
              <el-select v-model="form.cooperationMode" placeholder="请选择">
                <el-option label="项目合作" value="项目合作" />
                <el-option label="渠道合作" value="渠道合作" />
                <el-option label="战略合作" value="战略合作" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="我方签约人" prop="signer">
              <el-select v-model="form.signer" placeholder="请选择" clearable>
                <el-option
                  v-for="item in signerOptions"
                  :key="item.id"
                  :label="item.nickname"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="合同编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同开始日期" prop="contractStart">
              <el-date-picker
                v-model="form.contractStart"
                type="date"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同到期日期" prop="contractEnd">
              <el-date-picker
                v-model="form.contractEnd"
                type="date"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保证金" prop="deposit">
              <el-input v-model="form.deposit" placeholder="保证金金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="续约提醒日期" prop="renewDate">
              <el-date-picker
                v-model="form.renewDate"
                type="date"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 资质与结算信息 -->
      <div class="form-section">
        <div class="form-group-title">资质与结算信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="对公账户名" prop="accountName">
              <el-input v-model="form.accountName" placeholder="对公账户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算周期" prop="settlementCycle">
              <el-select v-model="form.settlementCycle" placeholder="请选择">
                <el-option label="月结" value="月结" />
                <el-option label="季结" value="季结" />
                <el-option label="年结" value="年结" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bankName">
              <el-input v-model="form.bankName" placeholder="开户银行名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="银行账号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="资质文件" prop="qualificationFile">
          <el-upload
            class="upload-demo"
            drag
            :show-file-list="true"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-preview="previewFile"
            :file-list="form.qualificationFile"
          >
            <el-button>选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">未选择任何文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 开票信息 -->
      <div class="form-section">
        <div class="form-group-title form-group-title-blue">开票信息</div>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="开票类型" prop="invoiceType">
              <el-select v-model="form.invoiceType" placeholder="请选择">
                <el-option label="企业" value="企业" />
                <el-option label="个人" value="个人" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票名称" prop="invoiceName">
              <el-input v-model="form.invoiceName" placeholder="开票抬头名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纳税人识别号" prop="taxId">
              <el-input v-model="form.taxId" placeholder="统一社会信用代码或税号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="社会组织代码" prop="orgCode">
              <el-input v-model="form.orgCode" placeholder="社会组织代码（如适用）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票地址" prop="invoiceAddress">
              <el-input v-model="form.invoiceAddress" placeholder="开票地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票电话" prop="invoicePhone">
              <el-input v-model="form.invoicePhone" placeholder="开票联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="invoiceBank">
              <el-input v-model="form.invoiceBank" placeholder="开户银行名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="invoiceBankAccount">
              <el-input v-model="form.invoiceBankAccount" placeholder="银行账号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票邮箱" prop="invoiceEmail">
              <el-input v-model="form.invoiceEmail" placeholder="开票邮箱地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票联系人" prop="invoiceContact">
              <el-input v-model="form.invoiceContact" placeholder="开票联系人姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="开票资质文件" prop="invoiceQualificationFile">
          <el-upload
            class="upload-demo"
            drag
            :show-file-list="true"
            :auto-upload="false"
            :on-change="handleInvoiceFileChange"
            :on-preview="previewFile"
            :file-list="form.invoiceQualificationFile"
          >
            <el-button>选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">未选择任何文件</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="开票备注" prop="invoiceRemark">
          <el-input
            v-model="form.invoiceRemark"
            type="textarea"
            :rows="2"
            placeholder="开票要求、注意事项等"
          />
        </el-form-item>
      </div>

      <!-- 底部按钮 -->
      <div class="form-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, watch } from 'vue'
import {
  createPartner,
  createPartnerLog,
  updatePartner,
  uploadFile
} from '@/api/infra/business/partner'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import type { UploadUserFile } from 'element-plus'
import { getUserPage } from '@/api/system/user'
import request from '@/config/axios'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { getSimpleDeptList } from '@/api/system/dept'
import * as AreaApi from '@/api/system/area'
import { defaultProps } from '@/utils/tree'

const userStore = useUserStore()

const visible = ref(false)
const formRef = ref()
const emit = defineEmits(['success'])

const mode = ref('add')
const partner = ref<any>(null)

const drawerTitle = ref('新增合作伙伴')
watch(mode, (val) => {
  drawerTitle.value = val === 'edit' ? '编辑合作伙伴' : '新增合作伙伴'
})

// 表单数据
const form = reactive({
  name: '',
  shortName: '',
  type: '',
  bizModule: '', // 新增业务模块字段
  status: '',
  legalPerson: '',
  foundationDate: '',
  creditCode: '',
  registeredCapital: undefined,
  registerAddress: '',
  businessAddress: '',
  mainBusiness: '',
  contactName: '',
  contactPhone: '',
  // 地址和位置相关字段
  areaId: undefined as any,
  provinceCode: '',
  province: '',
  cityCode: '',
  city: '',
  districtCode: '',
  district: '',
  detailAddress: '',
  longitude: undefined,
  latitude: undefined,
  rating: 0,
  cooperationMode: '',
  signer: undefined as number | undefined,
  contractNo: '',
  contractStart: '',
  contractEnd: '',
  deposit: '',
  renewDate: '',
  accountName: '',
  settlementCycle: '',
  bankName: '',
  bankAccount: '',
  qualificationFile: [] as UploadUserFile[],
  invoiceType: '',
  invoiceName: '',
  taxId: '',
  orgCode: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceEmail: '',
  invoiceContact: '',
  invoiceQualificationFile: [] as UploadUserFile[],
  invoiceRemark: ''
})

// 校验规则
const rules = {
  name: [{ required: true, message: '请输入机构全称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择机构类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择合作状态', trigger: 'change' }],
  bizModule: [{ required: true, message: '请选择业务模块', trigger: 'change' }], // 新增必填校验
  creditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  registeredCapital: [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
  // 新增必填字段验证规则
  foundationDate: [{ required: true, message: '请选择成立日期', trigger: 'change' }],
  mainBusiness: [{ required: true, message: '请输入主营业务', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入主要联系人', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  areaId: [{ required: true, message: '请选择所在地区', trigger: 'change' }],
  detailAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { max: 200, message: '详细地址不能超过200个字符', trigger: 'blur' }
  ],
  invoiceName: [{ required: true, message: '请输入开票名称', trigger: 'blur' }],
  taxId: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
  invoiceType: [{ required: true, message: '请选择开票类型', trigger: 'change' }],
  // 地址相关校验规则
  longitude: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && (value < -180 || value > 180)) {
          callback(new Error('经度范围应在-180到180之间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  latitude: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== undefined && value !== null && (value < -90 || value > 90)) {
          callback(new Error('纬度范围应在-90到90之间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 地区相关变量
const areaList = ref([]) // 地区列表

// 使用默认的级联选择器配置（emitPath: false，只返回最后选中的节点值）

// 地区选择变化处理
const handleAreaChange = (value: any) => {
  console.log('🔍 地区选择变化:', value)
  console.log('🔍 value类型:', typeof value)
  console.log('🔍 areaList数据长度:', areaList.value?.length)

  // 清空所有地区字段
  form.provinceCode = ''
  form.province = ''
  form.cityCode = ''
  form.city = ''
  form.districtCode = ''
  form.district = ''

  if (value) {
    console.log('🔍 开始处理地区选择，区县代码:', value)

    // 递归查找地区信息的函数
    const findAreaInfo = (areas: any[], targetId: any): any => {
      if (!areas || !Array.isArray(areas)) return null

      for (const area of areas) {
        if (String(area.id) === String(targetId)) {
          return area
        }
        if (area.children && area.children.length > 0) {
          const found = findAreaInfo(area.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    // 查找完整的地区路径信息
    const findAreaPath = (areas: any[], targetId: any, path: any[] = []): any => {
      for (const area of areas) {
        const currentPath = [...path, area]

        if (String(area.id) === String(targetId)) {
          return currentPath
        }

        if (area.children && area.children.length > 0) {
          const found = findAreaPath(area.children, targetId, currentPath)
          if (found) return found
        }
      }
      return null
    }

    try {
      // 查找区县及其完整路径
      const areaPath = findAreaPath(areaList.value, value)
      console.log('🔍 地区路径信息:', areaPath)

      if (areaPath && areaPath.length >= 3) {
        const [province, city, district] = areaPath

        // 设置省份信息
        form.provinceCode = String(province.id)
        form.province = province.name
        console.log('✅ 省份设置成功:', { code: form.provinceCode, name: form.province })

        // 设置城市信息
        form.cityCode = String(city.id)
        form.city = city.name
        console.log('✅ 城市设置成功:', { code: form.cityCode, name: form.city })

        // 设置区县信息
        form.districtCode = String(district.id)
        form.district = district.name
        console.log('✅ 区县设置成功:', { code: form.districtCode, name: form.district })
      } else {
        console.log('❌ 未找到完整的地区路径信息')
      }
    } catch (error) {
      console.error('❌ 处理地区选择时出错:', error)
    }
  } else {
    console.log('🔄 清空地区信息（无效选择）')
  }

  // 打印最终的地区字段值
  console.log('📋 最终地区字段值:', {
    areaId: form.areaId,
    provinceCode: form.provinceCode,
    province: form.province,
    cityCode: form.cityCode,
    city: form.city,
    districtCode: form.districtCode,
    district: form.district
  })
}

// 文件上传事件
const handleFileChange = async (file: UploadUserFile) => {
  if (file && file.raw) {
    const res = await uploadFile(file.raw)
    if (res) {
      form.qualificationFile = [{ name: file.name, url: res }]
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  }
}
const handleInvoiceFileChange = async (file: UploadUserFile) => {
  if (file && file.raw) {
    const res = await uploadFile(file.raw)
    if (res) {
      form.invoiceQualificationFile = [{ name: file.name, url: res }]
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  }
}
// 文件预览/下载
const previewFile = (file: any) => {
  window.open(file.url, '_blank')
}

// 签约人选项
const signerOptions = ref<{ id: number; nickname: string }[]>([])

const loadSignerOptions = async () => {
  const params = { pageNo: 1, pageSize: 20, status: 0 }
  const res = await getUserPage(params)

  const list = res.list ?? []
  // 关键：slice拷贝触发响应式
  signerOptions.value = list.map((item: any) => ({ id: item.id, nickname: item.nickname })).slice()
}

const orgTypeOptions = ref<{ label: string; value: string }[]>([])

const loadOrgTypeOptions = async () => {
  const res = await getDictDataPage({ dictType: 'institutional_type', pageNo: 1, pageSize: 50 })

  const list = res.list ?? []
  orgTypeOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const statusOptions = ref<{ label: string; value: string }[]>([])

const loadStatusOptions = async () => {
  const res = await getDictDataPage({ dictType: 'cooperative_status', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  statusOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

const bizModuleOptions = ref<{ label: string; value: string }[]>([])
const loadBizModuleOptions = async () => {
  const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 50 })
  const list = res?.list ?? []
  bizModuleOptions.value = list.map((item: any) => ({ label: item.label, value: item.value }))
}

// 加载地区数据
const loadAreaOptions = async () => {
  try {
    console.log('🔄 开始加载地区数据...')
    areaList.value = await AreaApi.getAreaTree()
    console.log('✅ 地区数据加载成功，总省份数:', areaList.value?.length)

    // 详细分析地区数据
    if (areaList.value && areaList.value.length > 0) {
      console.log('📊 地区数据分析:')
      console.log(
        '前5个省份:',
        areaList.value.slice(0, 5).map((p) => ({ id: p.id, name: p.name }))
      )

      // 查找河北省
      const hebei = areaList.value.find((p) => p.id === 130000)
      if (hebei) {
        console.log('✅ 找到河北省:', hebei.name, '城市数量:', hebei.children?.length)
        const tangshan = hebei.children?.find((c) => c.id === 130200)
        if (tangshan) {
          console.log('✅ 找到唐山市:', tangshan.name, '区县数量:', tangshan.children?.length)
          const districts = tangshan.children?.slice(0, 5).map((d) => ({ id: d.id, name: d.name }))
          console.log('唐山市前5个区县:', districts)
        } else {
          console.log('❌ 未找到唐山市 (130200)')
        }
      } else {
        console.log('❌ 未找到河北省 (130000)')
        console.log(
          '所有省份ID:',
          areaList.value.map((p) => p.id)
        )
      }
    }
  } catch (error) {
    console.error('❌ 加载地区数据失败:', error)
    areaList.value = []
  }
}

// 处理地区数据回显
const handleAreaDataEcho = async (partnerData: any) => {
  console.log('🔄 开始处理地区数据回显:', {
    provinceCode: partnerData.provinceCode,
    cityCode: partnerData.cityCode,
    districtCode: partnerData.districtCode
  })

  // 调试：检查地区数据结构
  console.log('🔍 当前地区数据状态:', {
    areaListLength: areaList.value?.length,
    firstProvince: areaList.value?.[0],
    firstCity: areaList.value?.[0]?.children?.[0],
    firstDistrict: areaList.value?.[0]?.children?.[0]?.children?.[0]
  })

  // 额外调试：检查地区数据的ID格式
  if (areaList.value && areaList.value.length > 0) {
    const sampleProvince = areaList.value[0]
    const sampleCity = sampleProvince?.children?.[0]
    const sampleDistrict = sampleCity?.children?.[0]

    console.log('🔍 地区数据ID格式分析:', {
      provinceId: { value: sampleProvince?.id, type: typeof sampleProvince?.id },
      cityId: { value: sampleCity?.id, type: typeof sampleCity?.id },
      districtId: { value: sampleDistrict?.id, type: typeof sampleDistrict?.id },
      targetDistrictCode: { value: partnerData.districtCode, type: typeof partnerData.districtCode }
    })
  }

  // 测试：尝试查找一个已知的区县代码
  if (partnerData.districtCode) {
    const testFind = (areas: any[], targetId: any, path: any[] = []): any => {
      for (const area of areas) {
        const currentPath = [...path, area]
        if (String(area.id) === String(targetId)) {
          return currentPath
        }
        if (area.children && area.children.length > 0) {
          const found = testFind(area.children, targetId, currentPath)
          if (found) return found
        }
      }
      return null
    }

    const testResult = testFind(areaList.value, partnerData.districtCode)
    console.log('🧪 测试查找区县代码:', partnerData.districtCode, '结果:', testResult)

    // 尝试多种匹配策略
    if (!testResult) {
      console.log('🔍 尝试其他匹配策略...')

      // 策略1：数字匹配
      const numericDistrictCode = Number(partnerData.districtCode)
      const numericResult = testFind(areaList.value, numericDistrictCode)
      console.log('🧪 数字匹配结果:', numericResult)
      console.log('🧪 转换后的数字代码:', numericDistrictCode, '类型:', typeof numericDistrictCode)

      // 策略2：模糊匹配（查找包含该代码的区县）
      const fuzzyFind = (areas: any[], targetId: any, path: any[] = []): any => {
        for (const area of areas) {
          const currentPath = [...path, area]
          const areaIdStr = String(area.id)
          const targetIdStr = String(targetId)

          if (areaIdStr.includes(targetIdStr) || targetIdStr.includes(areaIdStr)) {
            console.log('🔍 模糊匹配找到:', {
              areaId: area.id,
              targetId,
              path: currentPath.map((p) => p.name)
            })
          }

          if (area.children && area.children.length > 0) {
            fuzzyFind(area.children, targetId, currentPath)
          }
        }
      }

      console.log('🧪 开始模糊匹配...')
      fuzzyFind(areaList.value, partnerData.districtCode)
    }

    // 如果找不到，让我们看看地区数据的实际结构
    if (!testResult) {
      console.log('🔍 查找失败，分析地区数据结构:')
      console.log(
        '🔍 省份数据示例:',
        areaList.value?.slice(0, 2)?.map((p) => ({
          id: p.id,
          name: p.name,
          type: typeof p.id,
          childrenCount: p.children?.length
        }))
      )

      // 查找河北省（130000）的数据
      const hebei = areaList.value?.find((p) => String(p.id).startsWith('13'))
      if (hebei) {
        console.log('🔍 河北省数据:', {
          id: hebei.id,
          name: hebei.name,
          type: typeof hebei.id,
          cities: hebei.children?.slice(0, 3)?.map((c) => ({
            id: c.id,
            name: c.name,
            type: typeof c.id,
            districtsCount: c.children?.length
          }))
        })

        // 查找唐山市（130200）的数据
        const tangshan = hebei.children?.find((c) => String(c.id).startsWith('1302'))
        if (tangshan) {
          console.log('🔍 唐山市数据:', {
            id: tangshan.id,
            name: tangshan.name,
            type: typeof tangshan.id,
            districts: tangshan.children?.slice(0, 5)?.map((d) => ({
              id: d.id,
              name: d.name,
              type: typeof d.id
            }))
          })
        }
      }
    }
  }

  // 清空地区相关字段
  form.areaId = undefined
  form.provinceCode = ''
  form.province = ''
  form.cityCode = ''
  form.city = ''
  form.districtCode = ''
  form.district = ''

  // 检查是否有完整的省市区代码
  if (!partnerData.provinceCode || !partnerData.cityCode || !partnerData.districtCode) {
    console.log('⚠️ 地区代码不完整，跳过回显')
    return
  }

  // 临时方案：直接使用后端返回的数据，不进行地区数据验证
  console.log('🚀 使用临时方案：直接设置地区字段')
  form.areaId = Number(partnerData.districtCode)
  form.provinceCode = String(partnerData.provinceCode || '')
  form.province = partnerData.province || ''
  form.cityCode = String(partnerData.cityCode || '')
  form.city = partnerData.city || ''
  form.districtCode = String(partnerData.districtCode || '')
  form.district = partnerData.district || ''

  console.log('✅ 临时方案设置完成:', {
    areaId: form.areaId,
    province: `${form.provinceCode} - ${form.province}`,
    city: `${form.cityCode} - ${form.city}`,
    district: `${form.districtCode} - ${form.district}`
  })

  // 继续执行原有的调试逻辑，用于分析问题

  // 确保地区数据已加载
  if (!areaList.value || areaList.value.length === 0) {
    console.log('⚠️ 地区数据未加载，等待加载完成...')
    // 尝试重新加载地区数据
    try {
      await loadAreaOptions()
    } catch (error) {
      console.error('❌ 重新加载地区数据失败:', error)
    }

    // 再次检查
    if (!areaList.value || areaList.value.length === 0) {
      console.log('❌ 地区数据仍未加载，无法回显')
      return
    }
  }

  try {
    // 验证地区代码是否存在于地区数据中（使用数字匹配）
    const validateAreaCodes = (
      provinceCode: string,
      cityCode: string,
      districtCode: string
    ): any => {
      // 转换为数字进行匹配
      const numProvinceCode = Number(provinceCode)
      const numCityCode = Number(cityCode)
      const numDistrictCode = Number(districtCode)

      console.log('🔍 转换后的数字代码:', { numProvinceCode, numCityCode, numDistrictCode })

      // 查找省份
      const province: any = areaList.value.find((p: any) => p.id === numProvinceCode)
      if (!province) {
        console.log('❌ 未找到省份:', provinceCode, '(数字:', numProvinceCode, ')')
        return null
      }

      // 查找城市
      const city: any = province.children?.find((c: any) => c.id === numCityCode)
      if (!city) {
        console.log('❌ 未找到城市:', cityCode, '(数字:', numCityCode, ')')
        return null
      }

      // 查找区县
      const district: any = city.children?.find((d: any) => d.id === numDistrictCode)
      if (!district) {
        console.log('❌ 未找到区县:', districtCode, '(数字:', numDistrictCode, ')')
        return null
      }

      console.log('✅ 找到完整地区信息:', {
        province: province.name,
        city: city.name,
        district: district.name
      })
      return { province, city, district }
    }

    const areaInfo = validateAreaCodes(
      partnerData.provinceCode,
      partnerData.cityCode,
      partnerData.districtCode
    )

    if (areaInfo) {
      // 设置级联选择器的值（areaId 应该等于 districtCode，数字类型）
      // 根据业务逻辑，areaId 存储的是最终选中的区县代码
      form.areaId = Number(partnerData.districtCode)

      // 设置地区字段值
      form.provinceCode = String(partnerData.provinceCode)
      form.province = areaInfo.province.name
      form.cityCode = String(partnerData.cityCode)
      form.city = areaInfo.city.name
      form.districtCode = String(partnerData.districtCode)
      form.district = areaInfo.district.name

      console.log('✅ 地区数据回显成功:', {
        areaId: form.areaId,
        province: `${form.provinceCode} - ${form.province}`,
        city: `${form.cityCode} - ${form.city}`,
        district: `${form.districtCode} - ${form.district}`
      })
    } else {
      console.log('❌ 地区代码验证失败，尝试备用方案...')

      // 备用方案：直接设置字段值，不验证地区数据
      // 这样至少可以保证字段值正确，即使级联选择器不能回显
      form.provinceCode = String(partnerData.provinceCode || '')
      form.province = partnerData.province || ''
      form.cityCode = String(partnerData.cityCode || '')
      form.city = partnerData.city || ''
      form.districtCode = String(partnerData.districtCode || '')
      form.district = partnerData.district || ''

      // 尝试设置 areaId，即使级联选择器可能不会正确显示
      form.areaId = Number(partnerData.districtCode)

      console.log('⚠️ 使用备用方案设置地区字段:', {
        areaId: form.areaId,
        province: `${form.provinceCode} - ${form.province}`,
        city: `${form.cityCode} - ${form.city}`,
        district: `${form.districtCode} - ${form.district}`
      })
    }
  } catch (error) {
    console.error('❌ 地区数据回显处理出错:', error)
  }
}

// 取消
const onCancel = () => {
  visible.value = false
}
// 提交
const onSubmit = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    // 调试：打印提交前的form对象
    console.log('🚀 提交前的form对象:', form)
    console.log('🚀 提交前的地区字段:', {
      provinceCode: form.provinceCode,
      province: form.province,
      cityCode: form.cityCode,
      city: form.city,
      districtCode: form.districtCode,
      district: form.district
    })

    // 1. 构造接口文档要求的字段
    const partnerData = {
      ...(mode.value === 'edit' && partner.value && partner.value.id
        ? { id: partner.value.id }
        : {}),
      name: form.name,
      shortName: form.shortName,
      type: form.type,
      biz: form.bizModule, // 这里用bizModule
      status: form.status,
      risk: '', // 风险等级，表单无，留空
      owner: form.signer ? String(form.signer) : '0',
      ownerName: form.signer
        ? signerOptions.value.find((item) => item.id === form.signer)?.nickname || ''
        : '',
      legalPerson: form.legalPerson,
      foundationDate: form.foundationDate,
      creditCode: form.creditCode,
      registeredCapital: form.registeredCapital,
      registerAddress: form.registerAddress,
      businessAddress: form.businessAddress,
      mainBusiness: form.mainBusiness,
      contactName: form.contactName,
      contactPhone: form.contactPhone,
      // 地址和位置相关字段
      provinceCode: form.provinceCode,
      province: form.province,
      cityCode: form.cityCode,
      city: form.city,
      districtCode: form.districtCode,
      district: form.district,
      detailAddress: form.detailAddress,
      longitude: form.longitude,
      latitude: form.latitude,
      rating: form.rating,
      cooperationMode: form.cooperationMode,
      contractNo: form.contractNo,
      contractStart: form.contractStart,
      contractEnd: form.contractEnd,
      deposit: form.deposit,
      renewDate: form.renewDate,
      accountName: form.accountName,
      settlementCycle: form.settlementCycle,
      bankName: form.bankName,
      bankAccount: form.bankAccount,
      qualificationFile: form.qualificationFile?.[0]?.url || '',
      invoiceType: form.invoiceType,
      invoiceName: form.invoiceName,
      taxId: form.taxId,
      orgCode: form.orgCode,
      invoiceAddress: form.invoiceAddress,
      invoicePhone: form.invoicePhone,
      invoiceBank: form.invoiceBank,
      invoiceBankAccount: form.invoiceBankAccount,
      invoiceEmail: form.invoiceEmail,
      invoiceContact: form.invoiceContact,
      invoiceQualificationFile: form.invoiceQualificationFile?.[0]?.url || '',
      invoiceRemark: form.invoiceRemark
    }

    // 调试：打印构造的partnerData对象
    console.log('📤 构造的partnerData对象:', partnerData)
    console.log('📤 partnerData中的地区字段:', {
      provinceCode: partnerData.provinceCode,
      province: partnerData.province,
      cityCode: partnerData.cityCode,
      city: partnerData.city,
      districtCode: partnerData.districtCode,
      district: partnerData.district
    })

    try {
      let res
      if (mode.value === 'edit') {
        res = await updatePartner(partnerData)
      } else {
        res = await createPartner(partnerData)
      }
      if (res) {
        ElMessage.success(mode.value === 'edit' ? '更新成功' : '新增成功')
        visible.value = false
        emit('success')
        return
      }
      throw new Error(res.msg || (mode.value === 'edit' ? '更新失败' : '新增失败'))
    } catch (e: any) {
      ElMessage.error(e.message || '操作失败')
    }
  })
}

const open = async (_mode = 'add', partnerData: any = null) => {
  mode.value = _mode
  partner.value = partnerData
  // 重置表单字段
  Object.assign(form, {
    name: '',
    shortName: '',
    type: '',
    bizModule: '', // 新增业务模块字段
    status: '',
    legalPerson: '',
    foundationDate: '',
    creditCode: '',
    registeredCapital: undefined,
    registerAddress: '',
    businessAddress: '',
    mainBusiness: '',
    contactName: '',
    contactPhone: '',
    // 地址和位置相关字段重置
    areaId: undefined as any,
    provinceCode: '',
    province: '',
    cityCode: '',
    city: '',
    districtCode: '',
    district: '',
    detailAddress: '',
    longitude: undefined,
    latitude: undefined,
    rating: 0,
    cooperationMode: '',
    signer: undefined,
    contractNo: '',
    contractStart: '',
    contractEnd: '',
    deposit: '',
    renewDate: '',
    accountName: '',
    settlementCycle: '',
    bankName: '',
    bankAccount: '',
    qualificationFile: [],
    invoiceType: '',
    invoiceName: '',
    taxId: '',
    orgCode: '',
    invoiceAddress: '',
    invoicePhone: '',
    invoiceBank: '',
    invoiceBankAccount: '',
    invoiceEmail: '',
    invoiceContact: '',
    invoiceQualificationFile: [],
    invoiceRemark: ''
  })
  if (mode.value === 'edit' && partnerData) {
    console.log('🔄 开始编辑模式数据回显:', partnerData)
    console.log('🔍 后端返回的地区字段:', {
      provinceCode: partnerData.provinceCode,
      province: partnerData.province,
      cityCode: partnerData.cityCode,
      city: partnerData.city,
      districtCode: partnerData.districtCode,
      district: partnerData.district
    })

    Object.assign(form, partnerData)
    form.signer = partnerData.owner ? Number(partnerData.owner) : undefined
    // 业务模块字段映射：后端返回 biz，前端使用 bizModule
    form.bizModule = partnerData.biz || ''
    console.log('🔍 业务模块字段映射:', {
      backendBiz: partnerData.biz,
      frontendBizModule: form.bizModule
    })
    // 资质文件回显（强类型防御）
    if (Array.isArray(partnerData.qualificationFile)) {
      form.qualificationFile = partnerData.qualificationFile.filter(
        (f) => typeof f === 'object' && f !== null
      )
    } else if (
      typeof partnerData.qualificationFile === 'string' &&
      partnerData.qualificationFile.trim() &&
      /^https?:\/\//.test(partnerData.qualificationFile)
    ) {
      form.qualificationFile = [
        {
          name: '资质文件',
          url: partnerData.qualificationFile,
          uid: Date.now() // 修正为 number 类型
        }
      ]
    } else {
      form.qualificationFile = []
    }
    if (Array.isArray(partnerData.invoiceQualificationFile)) {
      form.invoiceQualificationFile = partnerData.invoiceQualificationFile.filter(
        (f) => typeof f === 'object' && f !== null
      )
    } else if (
      typeof partnerData.invoiceQualificationFile === 'string' &&
      partnerData.invoiceQualificationFile.trim() &&
      /^https?:\/\//.test(partnerData.invoiceQualificationFile)
    ) {
      form.invoiceQualificationFile = [
        {
          name: '开票资质文件',
          url: partnerData.invoiceQualificationFile,
          uid: Date.now() // 修正为 number 类型
        }
      ]
    } else {
      form.invoiceQualificationFile = []
    }

    // 地区字段回显处理
    await handleAreaDataEcho(partnerData)

    // 验证地区回显结果
    console.log('🔍 地区回显完成后的form状态:', {
      areaId: form.areaId,
      provinceCode: form.provinceCode,
      province: form.province,
      cityCode: form.cityCode,
      city: form.city,
      districtCode: form.districtCode,
      district: form.district
    })
  }
  drawerTitle.value = mode.value === 'edit' ? '编辑合作伙伴' : '新增合作伙伴'
  signerOptions.value = [] // 先清空，防止脏数据
  visible.value = true
  await Promise.all([
    loadSignerOptions(),
    loadOrgTypeOptions(),
    loadStatusOptions(),
    loadBizModuleOptions(), // 新增业务模块选项加载
    loadAreaOptions() // 加载地区数据
  ])
}

defineExpose({ open })
</script>

<style scoped lang="scss">
.partner-form {
  padding-right: 8px;
  max-height: 80vh;
  overflow-y: auto;
}
.form-section {
  background: #f7fafd;
  border-radius: 10px;
  padding: 18px 18px 8px 18px;
  margin-bottom: 20px;
}
.form-group-title {
  font-weight: bold;
  font-size: 16px;
  margin: 18px 0 8px 0;
}
.form-group-title-blue {
  color: #409eff;
}
.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-bottom: 8px;
}
</style>
