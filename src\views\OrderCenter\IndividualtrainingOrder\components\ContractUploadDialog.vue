<!--
  页面名称：个人培训订单合同上传弹窗
  功能描述：支持电子合同和纸质合同的上传，与企业培训订单保持一致
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="contract-upload-container">
      <!-- 合同类型选择 -->
      <el-form-item label="合同类型" prop="contractType" style="margin-bottom: 20px">
        <el-radio-group v-model="form.contractType" @change="onContractTypeChange">
          <el-radio value="electronic">
            <el-icon color="#67c23a" style="margin-right: 4px"><Document /></el-icon>
            电子合同
          </el-radio>
          <el-radio value="paper">
            <el-icon color="#409eff" style="margin-right: 4px"><Upload /></el-icon>
            纸质合同
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 电子合同表单 -->
      <div v-if="form.contractType === 'electronic'" class="electronic-contract-form">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="合同名称" prop="contractName" required>
            <el-input
              v-model="form.contractName"
              placeholder="请输入合同名称"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="合同编号" prop="contractNumber" required>
            <el-input
              v-model="form.contractNumber"
              placeholder="请输入合同编号"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="签署日期" prop="signDate" required>
            <el-date-picker
              v-model="form.signDate"
              type="date"
              placeholder="年-月-日"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="合同金额" prop="contractAmount" required>
            <el-input
              v-model="form.contractAmount"
              placeholder="请输入合同金额"
              style="width: 100%"
              type="number"
              min="0"
              step="0.01"
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 纸质合同表单 -->
      <div v-if="form.contractType === 'paper'" class="paper-contract-form">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="合同附件" prop="contractFile" required>
            <el-upload
              ref="uploadRef"
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              :limit="1"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              class="contract-upload"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="upload-tip">支持格式: PDF、Word、JPG、PNG, 文件大小不超过10MB</div>
              </template>
            </el-upload>
          </el-form-item>

          <el-form-item label="合同编号" prop="contractNumber" required>
            <el-input
              v-model="form.contractNumber"
              placeholder="请输入合同编号"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="合同名称" prop="contractName" required>
            <el-input
              v-model="form.contractName"
              placeholder="请输入合同名称"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="签署日期" prop="signDate" required>
            <el-date-picker
              v-model="form.signDate"
              type="date"
              placeholder="年-月-日"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="合同金额" prop="contractAmount" required>
            <el-input
              v-model="form.contractAmount"
              placeholder="请输入合同金额"
              style="width: 100%"
              type="number"
              min="0"
              step="0.01"
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <!-- 文件上传状态显示 -->
        <div v-if="fileList.length > 0" class="file-status-section">
          <div class="file-status-header">
            <span class="status-title">文件上传状态</span>
          </div>
          <div class="file-status-items">
            <div v-for="(file, index) in fileList" :key="index" class="file-status-item">
              <div class="file-info">
                <el-icon class="file-icon"><Document /></el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
                <el-tag
                  :type="
                    file.status === 'success'
                      ? 'success'
                      : file.status === 'fail'
                        ? 'danger'
                        : 'warning'
                  "
                  size="small"
                >
                  {{
                    file.status === 'success'
                      ? '上传成功'
                      : file.status === 'fail'
                        ? '上传失败'
                        : '待上传'
                  }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type UploadFile, type UploadFiles } from 'element-plus'
import { Document, Upload } from '@element-plus/icons-vue'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { IndividualTrainingOrderApi } from '@/api/OrderCenter/IndividualtrainingOrder'

interface Props {
  visible: boolean
  orderData?: any
  isEdit?: boolean // 是否为编辑模式
  existingContract?: any // 现有合同信息
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null,
  isEdit: false,
  existingContract: null
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const loading = ref(false)
const fileList = ref<UploadFile[]>([])

// 上传配置（预留，后续集成真实上传功能）
const { uploadUrl } = useUpload('contract')

// 表单数据
const form = reactive({
  contractType: 'electronic',
  contractName: '',
  contractNumber: '',
  signDate: '',
  contractAmount: '',
  remark: '',
  contractFile: null as File | null,
  contractFileUrl: '' // 新增：用于存储文件URL
})

// 表单验证规则
const rules = {
  contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
  contractNumber: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  contractAmount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
  // 纸质合同时才需要验证附件，编辑模式下如果有contractFileUrl则不需要contractFile
  contractFile: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (form.contractType === 'paper') {
          // 编辑模式下，如果有contractFileUrl则不需要contractFile
          if (props.isEdit && form.contractFileUrl) {
            callback()
          } else if (!value && !form.contractFileUrl) {
            callback(new Error('请选择合同附件'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  if (props.isEdit) {
    return props.orderData
      ? `编辑合同 - ${props.orderData.orderNumber || props.orderData.orderNo}`
      : '编辑合同'
  }
  return props.orderData
    ? `上传合同 - ${props.orderData.orderNumber || props.orderData.orderNo}`
    : '上传合同'
})

// 监听visible变化，初始化表单数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      console.log('=== ContractUploadDialog: 弹窗打开 ===')
      console.log('isEdit:', props.isEdit)
      console.log('existingContract:', props.existingContract)
      console.log('orderData:', props.orderData)

      if (props.isEdit && props.existingContract) {
        // 编辑模式：回填现有合同信息
        console.log('=== 编辑模式：回填合同信息 ===')
        console.log('现有合同信息:', props.existingContract)

        // 解析日期数组
        const startDateArray = props.existingContract.startDate
        const endDateArray = props.existingContract.endDate

        let startDate = ''
        let endDate = ''

        if (Array.isArray(startDateArray) && startDateArray.length === 3) {
          startDate = `${startDateArray[0]}-${String(startDateArray[1]).padStart(2, '0')}-${String(startDateArray[2]).padStart(2, '0')}`
        }

        if (Array.isArray(endDateArray) && endDateArray.length === 3) {
          endDate = `${endDateArray[0]}-${String(endDateArray[1]).padStart(2, '0')}-${String(endDateArray[2]).padStart(2, '0')}`
        }

        // 回填表单数据
        form.contractName = props.existingContract.contractName || ''
        form.contractNumber = props.existingContract.contractNumber || ''
        form.signDate = startDate || endDate || '' // 使用开始日期或结束日期作为签署日期
        form.contractAmount = props.existingContract.amount?.toString() || ''
        form.remark = '' // 编辑时清空备注

        // 根据附件路径判断合同类型
        // 优先使用orderData中的contractFileUrl，然后是existingContract中的attachmentPath
        const attachmentUrl =
          props.orderData?.contractFileUrl || props.existingContract?.attachmentPath

        if (attachmentUrl) {
          form.contractType = 'paper'
          // 使用查看页面的附件下载URL
          form.contractFileUrl = attachmentUrl

          fileList.value = [
            {
              name: attachmentUrl, // 直接使用附件URL作为文件名显示
              url: attachmentUrl,
              status: 'success',
              uid: Date.now() // 添加uid避免重复
            } as any
          ]

          console.log('=== 使用查看页面的附件URL ===')
          console.log('attachmentUrl:', attachmentUrl)
          console.log('props.orderData?.contractFileUrl:', props.orderData?.contractFileUrl)
          console.log(
            'props.existingContract?.attachmentPath:',
            props.existingContract?.attachmentPath
          )
        } else {
          form.contractType = 'electronic'
          form.contractFileUrl = ''
          fileList.value = []
        }

        console.log('回填后的表单数据:', form)
        console.log('文件列表:', fileList.value)
        console.log(
          '文件列表中的文件名:',
          fileList.value.map((f) => f.name)
        )
      } else {
        // 新增模式：自动填充一些字段
        form.contractName = `${props.orderData?.studentName || '学员'} - ${props.orderData?.courseName || '课程'} 培训合同`
        form.contractAmount = props.orderData?.orderAmount?.toString() || ''
        form.contractNumber = props.orderData?.orderNumber || props.orderData?.orderNo || ''

        // 重置其他字段
        form.contractType = 'electronic'
        form.signDate = ''
        form.remark = ''
        form.contractFile = null
        form.contractFileUrl = '' // 新增：重置文件URL
        fileList.value = []
      }
    }
  }
)

// 合同类型变化处理
const onContractTypeChange = () => {
  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 文件大小格式化
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 文件选择处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  console.log('文件选择:', file)
  if (file.raw) {
    form.contractFile = file.raw
    // 在编辑模式下，保留现有文件URL作为备用
    if (!props.isEdit) {
      form.contractFileUrl = ''
      console.log('新增模式：清空contractFileUrl')
    } else {
      console.log('编辑模式：保留现有contractFileUrl作为备用')
    }
  }
}

// 文件移除处理
const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  console.log('文件移除:', file)
  form.contractFile = null
  // 清空文件URL
  form.contractFileUrl = ''
  console.log('用户移除文件，清空contractFileUrl')
}

// 文件上传方法
const uploadFile = async (file: File): Promise<string> => {
  try {
    console.log('开始上传文件:', file.name, file.size)

    // 调用真实的文件上传接口
    const request = (await import('@/config/axios')).default

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'contract')

    const response = await request.post({
      url: '/infra/file/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    console.log('文件上传成功:', response)
    console.log('response类型:', typeof response)
    console.log('response.data:', response.data)
    console.log('response.data类型:', typeof response.data)

    // 根据实际接口返回格式解析URL
    // 1. 如果response本身就是URL字符串
    if (typeof response === 'string') {
      console.log('解析到URL字符串(直接):', response)
      return response
    }

    // 2. 如果response.data存在
    if (response && response.data) {
      // 如果data直接是URL字符串
      if (typeof response.data === 'string') {
        console.log('解析到URL字符串(data):', response.data)
        return response.data
      }
      // 如果data是对象且包含url字段
      else if (response.data.url) {
        console.log('解析到URL对象:', response.data.url)
        return response.data.url
      }
      // 如果data是对象但没有url字段，尝试其他可能的字段
      else if (typeof response.data === 'object') {
        console.log('response.data的所有字段:', Object.keys(response.data))
        // 尝试常见的URL字段名
        const possibleUrlFields = ['url', 'fileUrl', 'downloadUrl', 'path', 'filePath']
        for (const field of possibleUrlFields) {
          if (response.data[field]) {
            console.log(`找到URL字段 ${field}:`, response.data[field])
            return response.data[field]
          }
        }
      }
    }

    console.error('无法解析文件URL，完整响应:', response)
    throw new Error('文件上传失败：未获取到文件URL')
  } catch (error) {
    console.error('文件上传失败:', error)
    throw new Error(`文件上传失败：${(error as any)?.message || '请重试'}`)
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    console.log('=== 提交前的表单数据 ===')
    console.log('form:', form)
    console.log('form.contractFileUrl:', form.contractFileUrl)
    console.log('isEdit:', props.isEdit)
    console.log('existingContract:', props.existingContract)

    if (form.contractType === 'paper' && !form.contractFile && !form.contractFileUrl) {
      ElMessage.warning('请选择合同附件')
      return
    }

    // 声明文件URL变量
    let contractFileUrl = form.contractFileUrl || ''

    // 根据合同类型调用不同的API
    if (form.contractType === 'electronic') {
      // 电子合同 - 调用合同确认接口
      try {
        console.log('=== 开始提交电子合同 ===')
        console.log('表单数据:', form)
        console.log('订单数据:', props.orderData)

        // 调用合同确认接口
        await IndividualTrainingOrderApi.confirmContract({
          orderId: props.orderData?.id || props.orderData?.orderId || 0,
          orderNo: props.orderData?.orderNo || '',
          contractType: form.contractType,
          contractName:
            form.contractName ||
            `${props.orderData?.studentName || '学员'} - ${props.orderData?.projectName || '培训'} 合同`,
          contractNumber: form.contractNumber || `CT${Date.now()}`,
          contractFileUrl: '', // 电子合同不需要文件
          contractStatus: 'signed',
          signer: props.orderData?.studentName || '系统',
          signDate: form.signDate || new Date().toISOString().split('T')[0],
          contractAmount: form.contractAmount || props.orderData?.orderAmount || 0,
          remark: form.remark || '电子合同确认'
        })

        console.log('合同确认接口调用成功')
        ElMessage.success(props.isEdit ? '电子合同更新成功' : '电子合同创建成功')
      } catch (error) {
        console.error('电子合同操作失败:', error)
        ElMessage.error(
          `电子合同${props.isEdit ? '更新' : '创建'}失败：${(error as any)?.message || '请重试'}`
        )
        return
      }
    } else {
      // 纸质合同 - 先上传文件，再调用合同确认接口
      try {
        console.log('=== 开始提交纸质合同 ===')
        console.log('表单数据:', form)
        console.log('form.contractFile:', form.contractFile)
        console.log('form.contractFileUrl:', form.contractFileUrl)
        console.log('订单数据:', props.orderData)

        // 确定合同文件URL
        if (form.contractFile) {
          // 用户选择了新文件，需要上传
          console.log('上传新选择的文件...')
          try {
            contractFileUrl = await uploadFile(form.contractFile)
            console.log('文件上传成功，获取到URL:', contractFileUrl)
          } catch (uploadError) {
            console.error('文件上传失败:', uploadError)
            // 如果上传失败，尝试使用现有URL（编辑模式）
            if (props.isEdit && props.existingContract?.attachmentPath) {
              contractFileUrl = props.existingContract.attachmentPath
              console.log('上传失败，使用现有URL:', contractFileUrl)
            } else {
              throw uploadError
            }
          }
        } else if (form.contractFileUrl) {
          // 使用现有文件URL（编辑模式）
          contractFileUrl = form.contractFileUrl
          console.log('使用现有文件URL:', contractFileUrl)
        } else if (props.isEdit && props.existingContract?.attachmentPath) {
          // 编辑模式下，使用现有合同附件URL
          contractFileUrl = props.existingContract.attachmentPath
          console.log('使用现有合同附件URL:', contractFileUrl)
        } else {
          // 没有文件也没有URL，报错
          throw new Error('请选择合同附件')
        }

        // 调用合同确认接口
        await IndividualTrainingOrderApi.confirmContract({
          orderId: props.orderData?.id || props.orderData?.orderId || 0,
          orderNo: props.orderData?.orderNo || '',
          contractType: form.contractType,
          contractName:
            form.contractName ||
            `${props.orderData?.studentName || '学员'} - ${props.orderData?.projectName || '培训'} 合同`,
          contractNumber: form.contractNumber || `CT${Date.now()}`,
          contractFileUrl: contractFileUrl,
          contractStatus: 'signed',
          signer: props.orderData?.studentName || '系统',
          signDate: form.signDate || new Date().toISOString().split('T')[0],
          contractAmount: form.contractAmount || props.orderData?.orderAmount || 0,
          remark: form.remark || '纸质合同确认'
        })

        console.log('合同确认接口调用成功')
        ElMessage.success(props.isEdit ? '纸质合同更新成功' : '纸质合同提交成功')
      } catch (error) {
        console.error('纸质合同操作失败:', error)
        ElMessage.error(
          `纸质合同${props.isEdit ? '更新' : '提交'}失败：${(error as any)?.message || '请重试'}`
        )
        return
      }
    }

    // 提交成功，发送事件
    const successData = {
      ...form,
      orderData: props.orderData,
      contractType: form.contractType,
      contractName: form.contractName,
      contractNumber: form.contractNumber,
      signDate: form.signDate,
      signingDate: form.signDate, // 保持向后兼容
      contractAmount: form.contractAmount,
      remark: form.remark,
      // 传递文件信息
      contractFile: form.contractFile,
      contractFileUrl: contractFileUrl || form.contractFileUrl
    }

    console.log('=== 提交成功，发送数据 ===')
    console.log('successData:', successData)
    console.log('contractFileUrl:', contractFileUrl)
    console.log('form.contractFileUrl:', form.contractFileUrl)

    emit('success', successData)
    visible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  if (props.isEdit && props.existingContract) {
    // 编辑模式：恢复到原始数据
    const startDateArray = props.existingContract.startDate
    const endDateArray = props.existingContract.endDate

    let startDate = ''
    let endDate = ''

    if (Array.isArray(startDateArray) && startDateArray.length === 3) {
      startDate = `${startDateArray[0]}-${String(startDateArray[1]).padStart(2, '0')}-${String(startDateArray[2]).padStart(2, '0')}`
    }

    if (Array.isArray(endDateArray) && endDateArray.length === 3) {
      endDate = `${endDateArray[0]}-${String(endDateArray[1]).padStart(2, '0')}-${String(endDateArray[2]).padStart(2, '0')}`
    }

    form.contractName = props.existingContract.contractName || ''
    form.contractNumber = props.existingContract.contractNumber || ''
    form.signDate = startDate || endDate || ''
    form.contractAmount = props.existingContract.amount?.toString() || ''
    form.remark = ''

    // 优先使用orderData中的contractFileUrl，然后是existingContract中的attachmentPath
    const attachmentUrl = props.orderData?.contractFileUrl || props.existingContract?.attachmentPath

    if (attachmentUrl) {
      form.contractType = 'paper'
      form.contractFileUrl = attachmentUrl
      fileList.value = [
        {
          name: attachmentUrl, // 直接使用附件URL作为文件名显示
          url: attachmentUrl,
          status: 'success'
        } as any
      ]
    } else {
      form.contractType = 'electronic'
      form.contractFileUrl = ''
      fileList.value = []
    }
  } else {
    // 新增模式：完全重置
    form.contractType = 'electronic'
    form.contractName = ''
    form.contractNumber = ''
    form.signDate = ''
    form.contractAmount = ''
    form.remark = ''
    form.contractFile = null
    form.contractFileUrl = '' // 新增：重置文件URL
    fileList.value = []
  }
  formRef.value?.clearValidate()
}
</script>

<style scoped lang="scss">
.contract-upload-container {
  .electronic-contract-form,
  .paper-contract-form {
    margin-top: 20px;
  }

  .contract-upload {
    .upload-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      margin-top: 8px;
    }
  }

  .file-status-section {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .file-status-header {
      margin-bottom: 16px;

      .status-title {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }
    }

    .file-status-items {
      .file-status-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e4e7ed;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;

          .file-icon {
            font-size: 16px;
            color: #909399;
          }

          .file-name {
            color: #303133;
            font-weight: 500;
            flex: 1;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-required .el-form-item__label:before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
