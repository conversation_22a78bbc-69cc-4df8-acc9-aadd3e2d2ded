<!--
  页面名称：机构管理
  功能描述：展示机构列表，支持搜索、分页、查看详情、编辑等操作
-->
<template>
  <div class="agency-management">
    <!-- 筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="机构名称/ID：">
        <el-input
          v-model="searchForm.nameOrId"
          placeholder="输入名称或ID"
          clearable
          style="width: 220px"
        />
      </el-form-item>
      <el-form-item label="合作状态：">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 100px">
          <el-option label="全部" :value="''" />
          <el-option label="合作中" value="cooperating" />
          <el-option label="已暂停" value="suspended" />
          <el-option label="已终止" value="terminated" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态：">
        <el-select v-model="searchForm.auditStatus" placeholder="全部" clearable style="width: 100px">
          <el-option label="全部" :value="''" />
          <el-option label="已通过" value="approved" />
          <el-option label="待审核" value="pending" />
          <el-option label="已拒绝" value="rejected" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区：">
        <el-input
          v-model="searchForm.region"
          placeholder="输入地区"
          clearable
          style="width: 180px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 机构列表 -->
    <div class="agency-list">
      <div class="list-header">
        <div class="list-cell checkbox">
          <el-checkbox v-model="selectAll" @change="handleSelectAll" />
        </div>
        <div class="list-cell name">机构名称/ID</div>
        <div class="list-cell status">合作状态</div>
        <div class="list-cell audit">审核状态</div>
        <div class="list-cell contact">联系人</div>
        <div class="list-cell region">所在地区</div>
        <div class="list-cell date">入驻日期</div>
        <div class="list-cell actions">操作</div>
      </div>

      <div class="list-body">
        <div
          v-for="agency in tableData"
          :key="agency.id"
          class="list-row"
          :class="{ active: selectedAgency?.id === agency.id }"
          @click="selectAgency(agency)"
        >
          <div class="list-cell checkbox">
            <el-checkbox v-model="agency.selected" @change="handleSelect" />
          </div>
          <div class="list-cell name">
            <strong>{{ agency.name }}</strong>
            <br />
            <small>ID: {{ agency.id }}</small>
          </div>
          <div class="list-cell status">
            <el-tag :type="agency.status === 'cooperating' ? 'success' : 'warning'" size="small">
              {{ agency.statusText }}
            </el-tag>
          </div>
          <div class="list-cell audit">
            <el-tag :type="agency.auditStatus === 'approved' ? 'success' : 'warning'" size="small">
              {{ agency.auditStatus === 'approved' ? '已通过' : (agency.auditStatus === 'pending' ? '待审核' : '已拒绝') }}
            </el-tag>
          </div>
          <div class="list-cell contact">
            {{ agency.contact }}
            <br />
            <small>{{ formatPhone(agency.phone) }}</small>
          </div>
          <div class="list-cell region">{{ agency.region }}</div>
          <div class="list-cell date">{{ formatDate(agency.joinDate) }}</div>
          <div class="list-cell actions">
            <el-button size="small" @click.stop="viewAgencyDetails(agency)"> 查看详情 </el-button>
            <el-button
              v-if="agency.auditStatus === 'pending' || agency.auditStatus === 'rejected'"
              size="small"
              type="primary"
              @click.stop="auditAgency(agency)"
            >
              审核
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination-wrap">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :current-page="pageNo"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 机构详情卡片 -->
    <div v-if="selectedAgency" class="agency-details-card">
      <div class="card-header">
        <h3>机构详情: {{ selectedAgency.name }}</h3>
      </div>

      <div class="card-body">
        <!-- 详情标签页 -->
        <el-tabs v-model="detailTab" class="detail-tabs" @tab-change="onTabChange">
          <el-tab-pane label="业务数据" name="data">
            <AgencyData :key="`data-${tabRefreshKey}`" :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="旗下阿姨" name="practitioners">
            <AgencyPractitioners :key="`practitioners-${tabRefreshKey}`" :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="激励/处罚记录" name="records">
            <AgencyIncentiveRecords :key="`records-${tabRefreshKey}`" :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="沟通日志" name="log">
            <AgencyCommunicationLog :key="`log-${tabRefreshKey}`" :agency="selectedAgency" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <el-dialog
      v-model="examineDialogVisible"
      title="机构注册审核"
      width="800px"
      :before-close="handleExamineDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="audit-dialog"
    >
      <AgencyExamine 
        v-if="examineDialogVisible" 
        :agency="examineAgency" 
        @close="examineDialogVisible = false"
        @success="handleExamineSuccess"
      />
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAgencyList } from '@/api/mall/employment/agency'
import AgencyData from './agency/AgencyData.vue'
import AgencyPractitioners from './agency/AgencyPractitioners.vue'
import AgencyIncentiveRecords from './agency/AgencyIncentiveRecords.vue'
import AgencyCommunicationLog from './agency/AgencyCommunicationLog.vue'
import AgencyLog from './agency/AgencyLog.vue'
import AgencyExamine from './agency/AgencyExamine.vue'
// mock数据引入
import { mockAgencyData } from '@/api/mall/employment/mockData'

/** 机构数据类型定义 */
interface Agency {
  id: number
  name: string
  status: string
  statusText: string
  auditStatus: string
  contact: string
  phone: string
  region: string
  joinDate: string
  selected?: boolean
}

/** 搜索表单数据 */
const searchForm = reactive({
  nameOrId: '',
  status: '',
  region: '',
  auditStatus: ''
})

/** 列表与分页数据 */
const tableData = ref<Agency[]>([])
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(0)

/** 选中的机构 */
const selectedAgency = ref<Agency | null>(null)

/** 全选状态 */
const selectAll = ref(false)

/** 详情标签页 */
const detailTab = ref('data')

/** 标签页刷新标识 */
const tabRefreshKey = ref(0)

/** 刷新状态 */
const refreshing = ref(false)

/** 审核弹窗相关 */
const examineDialogVisible = ref(false)
const examineAgency = ref<Agency | null>(null)



/** 获取机构列表 */
const fetchList = async () => {
  try {
    const params = {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      keyword: searchForm.nameOrId || undefined,
      cooperationStatus: searchForm.status || undefined,
      reviewStatus: searchForm.auditStatus || undefined,
      district: searchForm.region || undefined
    }
    const res: any = await getAgencyList(params as any)
    // 兼容不同返回形态：res 可能是 {list,total} 或 {data: { list, total }} 或 纯数组
    const rawList = Array.isArray(res)
      ? res
      : res?.list || res?.data?.list || res?.records || res?.data || []
    total.value = (res?.total ?? res?.data?.total ?? 0) as number
    if (res?.pageNo) pageNo.value = res.pageNo
    if (res?.pageSize) pageSize.value = res.pageSize
    tableData.value = (rawList || []).map((item: any) => {
      const cooperation = item.cooperation_status || item.cooperationStatus || 'pending'
      return {
        id: item.id,
        name: item.agency_name || item.agencyName || item.name || '-',
        status: cooperation,
        statusText:
          cooperation === 'cooperating'
            ? '合作中'
            : cooperation === 'suspended'
            ? '已暂停'
            : cooperation === 'terminated'
            ? '已终止'
            : '待审核',
        auditStatus: item.review_status || item.reviewStatus || 'pending',
        contact:
          item.contact_person || item.contactPerson || item.applicant_name || item.applicantName || '-',
        phone:
          item.contact_phone || item.contactPhone || item.applicant_phone || item.applicantPhone || '-',
        region: [item.city || item.cityName, item.district || item.districtName]
          .filter(Boolean)
          .join('-') || '-',
        joinDate:
          item.application_time || item.applicationTime || item.create_time || item.createTime || ''
      }
    })
  } catch (error) {
    console.error('获取机构列表失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  pageNo.value = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    nameOrId: '',
    status: '',
    region: '',
    auditStatus: ''
  })
  pageNo.value = 1
  fetchList()
}

/** 选择机构 */
const selectAgency = (agency: Agency) => {
  // 检查是否选择了新机构
  const isNewAgency = selectedAgency.value?.id !== agency.id
  
  selectedAgency.value = agency
  
  // 只有选择新机构时才重置标签页到第一个
  if (isNewAgency) {
    detailTab.value = 'data'
  }
  
  // 刷新数据
  refreshTabData()
}

/** 查看机构详情 */
const viewAgencyDetails = (agency: Agency) => {
  // 检查是否选择了新机构
  const isNewAgency = selectedAgency.value?.id !== agency.id
  
  selectedAgency.value = agency
  
  // 只有选择新机构时才重置标签页到第一个
  if (isNewAgency) {
    detailTab.value = 'data'
  }
  
  // 刷新数据
  refreshTabData()
}

/** 审核机构 */
const auditAgency = (agency: Agency) => {
  examineAgency.value = agency
  examineDialogVisible.value = true
}

/** 处理审核弹窗关闭 */
const handleExamineDialogClose = (done: () => void) => {
  ElMessageBox.confirm('确定要关闭审核页面吗？未保存的审核结果将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    examineAgency.value = null
    done()
  }).catch(() => {
    // 取消关闭
  })
}

/** 处理审核成功 */
const handleExamineSuccess = () => {
  examineDialogVisible.value = false
  examineAgency.value = null
  ElMessage.success('审核完成')
  // 刷新列表
  fetchList()
}

/** 刷新标签页数据 */
const refreshTabData = async () => {
  refreshing.value = true
  
  try {
    // 增加刷新标识，强制子组件重新渲染
    tabRefreshKey.value++
    
    // 等待一小段时间确保组件重新渲染
    await new Promise(resolve => setTimeout(resolve, 100))
    
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    refreshing.value = false
  }
}

/** 标签页切换事件 */
const onTabChange = (tabName: string) => {
  // 切换标签页时刷新当前标签页数据
  refreshTabData()
}

/** 全选处理 */
const handleSelectAll = (val: boolean) => {
  tableData.value.forEach((item) => {
    item.selected = val
  })
}

/** 单个选择处理 */
const handleSelect = () => {
  const selectedCount = tableData.value.filter((item) => item.selected).length
  selectAll.value = selectedCount === tableData.value.length
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  if (Number.isNaN(d.getTime())) return ''
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  const dd = String(d.getDate()).padStart(2, '0')
  return `${y}-${m}-${dd}`
}

/** 手机号脱敏 */
const formatPhone = (phone: string) => {
  if (!phone) return ''
  return String(phone).replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/** 分页变化 */
const handlePageChange = (p: number) => {
  pageNo.value = p
  fetchList()
}
const handleSizeChange = (s: number) => {
  pageSize.value = s
  pageNo.value = 1
  fetchList()
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.agency-management {
  .search-form {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    :deep(.el-form-item) {
      margin-right: 16px;
      margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
      color: #606266;
      padding-right: 8px;
    }
  }

  .agency-list {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 20px;
  }

  .list-header,
  .list-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
  }

  .list-row:last-child {
    border-bottom: none;
  }

  .list-header {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
  }

  .list-row:hover {
    background: #f5f7fa;
  }

  .list-row.active {
    background: rgba(52, 152, 219, 0.1);
  }

  .list-cell {
    padding: 0 10px;
  }

  .list-cell.checkbox {
    flex: 0 0 40px;
  }
  .list-cell.name {
    flex: 1 1 18%;
  }
  .list-cell.status {
    flex: 0 0 120px;
    text-align: center;
  }
  .list-cell.contact {
    flex: 1 1 20%;
  }
  .list-cell.region {
    flex: 1 1 15%;
  }
  .list-cell.date {
    flex: 1 1 15%;
  }
  .list-cell.actions {
    flex: 0 0 120px;
    text-align: right;
    
    .el-button {
      margin-left: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }

  .agency-details-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
  }

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
  }

  .card-body {
    padding: 20px;
  }

  .detail-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
  }

  .pagination-wrap {
    display: flex;
    justify-content: flex-end;
    margin: 16px 0;
  }
}
</style>
