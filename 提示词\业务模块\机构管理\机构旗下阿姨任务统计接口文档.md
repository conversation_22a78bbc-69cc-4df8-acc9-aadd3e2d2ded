# 机构管理模块API接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/employment/practitioner`
- **数据格式**: `application/json`
- **字符编码**: `UTF-8`

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 1.3 响应状态码说明

- `200`: 操作成功
- `400`: 参数验证错误
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 2. 接口列表

### 2.2 获取阿姨任务统计

- **接口地址**: `GET /publicbiz/employment/practitioner/taskStats/{auntOneid}`
- **接口描述**: 获取指定阿姨当月的任务统计数据
- **请求参数**:

| 参数名    | 类型   | 位置 | 必填 | 说明      | 示例值      |
| --------- | ------ | ---- | ---- | --------- | ----------- |
| auntOneid | String | Path | 是   | 阿姨OneID | aunt_123456 |
| month | String | Path | 是   | 月份 | 2025-08 |

- **响应字段**:

| 字段名    | 类型    | 说明                     | 示例值       |
| --------- | ------- | ------------------------ | ------------ |
| date      | String  | 日期（格式：yyyy-MM-dd） | "2024-01-15" |
| taskCount | Integer | 当天任务数               | 3            |

**响应示例**：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "date": "2024-01-01",
      "taskCount": 3
    },
    {
      "date": "2024-01-02",
      "taskCount": 0
    },
    {
      "date": "2024-01-03",
      "taskCount": 2
    }
  ]
}
```

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明         | 解决方案                 |
| ------ | ------------ | ------------------------ |
| 400    | 参数验证错误 | 检查请求参数格式和必填项 |
| 403    | 权限不足     | 确认用户具有相应操作权限 |
| 404    | 资源不存在   | 检查资讯ID是否正确       |
| 500    | 服务器错误   | 联系技术支持             |

### 4.2 业务错误码

| 错误码 | 说明                   | 解决方案                     |
| ------ | ---------------------- | ---------------------------- |
| 1001   | 机构名称已存在         | 修改机构名称                 |
| 1002   | 机构编号已存在         | 修改机构编号                 |
| 1003   | 统一社会信用代码已存在 | 检查统一社会信用代码是否正确 |
| 1004   | 机构状态不允许操作     | 检查当前状态是否允许该操作   |
| 1005   | 机构不存在             | 检查机构ID是否正确           |
| 1006   | 统计时间范围无效       | 检查时间范围参数是否正确     |
| 1007   | 阿姨不存在             | 检查阿姨OneID是否正确        |

## 5. 注意事项

1. **权限控制**: 所有接口都需要进行权限验证，确保用户具有相应操作权限
2. **数据验证**: 前端和后端都需要进行数据验证，确保数据完整性和正确性
3. **机构信息**: 机构名称、编号、统一社会信用代码等关键信息需要唯一性校验
4. **审核流程**: 机构审核需要严格按照审核流程执行，确保数据准确性
5. **统计性能**: 业务数据统计接口建议使用缓存优化，避免重复计算
6. **日志记录**: 重要操作需要记录操作日志，便于审计和问题排查
7. **数据安全**: 机构敏感信息需要进行脱敏处理，确保数据安全

## 6. 更新日志

| 版本  | 日期       | 更新内容                                       |
| ----- | ---------- | ---------------------------------------------- |
| 1.0.0 | 2024-01-20 | 初始版本，包含基础CRUD接口                     |
| 1.1.0 | 2024-01-25 | 新增机构审核状态管理接口                       |
| 1.2.0 | 2024-02-01 | 新增机构业务数据统计接口                       |
| 1.3.0 | 2024-02-20 | 优化统计接口，支持多种时间范围统计             |
| 1.4.0 | 2024-02-25 | 完善机构管理功能，增加数据验证和权限控制       |
| 1.5.0 | 2024-03-01 | 新增阿姨任务统计接口，支持查看阿姨每日任务数据 |
