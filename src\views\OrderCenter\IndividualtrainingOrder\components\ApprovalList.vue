<!--
  页面名称：个人培训订单审批列表
  功能描述：显示订单的审批记录和状态
-->
<template>
  <div class="approval-list">
    <div class="list-header">
      <h4>审批记录</h4>
      <div class="header-actions">
        <el-button size="small" type="primary" @click="showSubmitDialog = true">
          <el-icon><Plus /></el-icon>
          发起审批
        </el-button>
        <el-button size="small" @click="refresh">刷新</el-button>
      </div>
    </div>

    <div class="list-content" v-loading="loading">
      <div v-if="loading" class="loading"> 正在加载审批记录... </div>
      <div v-else-if="approvalList.length === 0" class="empty"> 暂无审批记录 </div>

      <div v-else class="timeline">
        <div v-for="item in approvalList" :key="item.approvalId" class="timeline-item">
          <div class="dot" :class="getStatusClass(item.approvalResult || item.status)"></div>
          <div class="content">
            <div class="approval-header">
              <div class="approval-no">审批单号：{{ item.approvalNo || '-' }}</div>
              <div class="time">{{ formatTime(item.createTime) }}</div>
            </div>
            <div class="approval-info">
              <div class="approval-type">
                <el-tag size="small" type="info">{{
                  getApprovalTypeText(item.approvalType)
                }}</el-tag>
                <span class="level">第{{ item.approvalLevel }}级审批</span>
              </div>
              <div class="action">
                {{ item.operatorName || item.approverName || '未知用户' }}
                {{ getActionText(item.approvalResult || item.status) }}
              </div>
              <div class="approval-time" v-if="item.approvalTime">
                审批时间：{{ formatTime(item.approvalTime) }}
              </div>
              <div class="opinion" v-if="item.approvalOpinion">
                审批意见：{{ item.approvalOpinion }}
              </div>
              <div class="reject-reason" v-if="item.rejectReason">
                拒绝原因：{{ item.rejectReason }}
              </div>
            </div>
            <div class="status">
              <el-tag :type="getTagType(item.approvalResult || item.status)" size="small">
                {{ getStatusText(item.approvalResult || item.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发起审批对话框 -->
    <SubmitApprovalDialog
      v-model:visible="showSubmitDialog"
      :order-data="orderData"
      @success="onApprovalSubmitted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { IndividualTrainingOrderApi } from '@/api/OrderCenter/IndividualtrainingOrder'
import SubmitApprovalDialog from './SubmitApprovalDialog.vue'

interface Props {
  orderId: string | number
  orderNo?: string
  orderData?: any
}

const props = defineProps<Props>()

// 审批列表数据
const approvalList = ref<any[]>([])
const loading = ref(false)
const showSubmitDialog = ref(false)

// 获取审批列表
const fetchApprovalList = async () => {
  if (!props.orderId) return

  try {
    loading.value = true

    // 从操作日志接口获取审批记录
    const orderNo = props.orderNo || ''
    const result = await IndividualTrainingOrderApi.getOptLogList({
      orderNo: orderNo,
      page: 1,
      size: 50
    })

    // 过滤出审批相关的日志
    let logs: any[] = []
    if (result && result.records) {
      logs = result.records
    } else if ((result as any).data && (result as any).data.list) {
      logs = (result as any).data.list
    } else if ((result as any).list) {
      logs = (result as any).list
    }

    // 过滤出logType包含"审批"的记录
    const approvalLogs = logs.filter((log: any) => log.logType && log.logType.includes('审批'))

    // 转换为审批列表格式
    approvalList.value = approvalLogs.map((log: any) => ({
      approvalId: log.id,
      approvalNo: log.logTitle || log.logType,
      approvalType: log.logType,
      approvalResult: log.newStatus || 'pending',
      operatorName: log.operatorName || '系统',
      createTime: log.createTime,
      approvalTime: log.createTime,
      approvalOpinion: log.logContent || ''
    }))

    console.log('审批记录列表:', approvalList.value)

    if (approvalList.value.length > 0) {
      ElMessage.success('审批列表获取成功')
    } else {
      ElMessage.info('暂无审批记录')
    }
  } catch (error) {
    console.error('获取审批列表失败:', error)
    ElMessage.error('获取审批列表失败')
    // 如果API调用失败，使用空数组
    approvalList.value = []
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  fetchApprovalList()
}

const onApprovalSubmitted = async (approvalData: any) => {
  try {
    // 审批提交成功后刷新列表
    await fetchApprovalList()
    ElMessage.success('审批申请提交成功')
  } catch (error) {
    console.error('刷新审批列表失败:', error)
  }
}

const getTagType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const map: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return map[status] || 'info'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已驳回'
  }
  return map[status] || '未知'
}

const getApprovalTypeText = (type: string) => {
  const map: Record<string, string> = {
    first: '初审',
    second: '复审',
    final: '终审'
  }
  return map[type] || '未知'
}

const getActionText = (status: string) => {
  const map: Record<string, string> = {
    pending: '发起审批',
    approved: '通过审批',
    rejected: '驳回审批'
  }
  return map[status] || '未知'
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const getStatusClass = (status: string) => {
  const map: Record<string, string> = {
    pending: 'pending',
    approved: 'approved',
    rejected: 'rejected'
  }
  return map[status] || 'info'
}

onMounted(() => {
  // 初始化时获取审批列表
  fetchApprovalList()
})
</script>

<style scoped>
.approval-list {
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h4 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-section .el-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-section .el-form-item {
  margin-bottom: 0;
}

.empty {
  text-align: center;
  color: #999;
  padding: 40px;
}

.loading {
  text-align: center;
  color: #666;
  padding: 40px;
}

.timeline {
  .timeline-item {
    display: flex;
    margin-bottom: 20px;

    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 16px;
      margin-top: 6px;

      &.pending {
        background: #e6a23c;
      }
      &.approved {
        background: #67c23a;
      }
      &.rejected {
        background: #f56c6c;
      }
    }

    .content {
      flex: 1;
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;

      .approval-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .approval-no {
          font-size: 14px;
          color: #333;
          font-weight: bold;
        }

        .time {
          font-size: 12px;
          color: #999;
        }
      }

      .approval-info {
        margin-bottom: 8px;

        .approval-type {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .level {
            margin-left: 8px;
            font-size: 12px;
            color: #666;
          }
        }

        .action {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
        }

        .approval-time {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }

        .opinion,
        .reject-reason {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>
