import request from '@/config/axios'

// ==================== 基础类型定义 ====================

// 订单基础信息
export interface IndividualTrainingOrder {
  id?: number
  orderNo?: string
  orderType: string
  businessLine?: string
  businessOpportunity?: string
  associatedLead?: string
  studentName: string
  studentPhone?: string
  studentEmail?: string
  studentOneid?: string // 学员身份证号/唯一标识，对应数据库 student_oneid 字段
  courseName: string
  courseType?: string
  orderSource?: string // 改为可选，因为后端数据中没有这个字段
  orderAmount: number
  orderStatus?: string
  paymentStatus: string
  learningStatus?: string
  examStatus?: string
  registrationTime?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 后端实际返回的订单数据结构
export interface BackendOrderData {
  id: number
  orderId: number
  orderNo: string
  studentName: string
  studentOneid: string
  studentPhone: string | null
  studentEmail: string | null
  studentIdCard: string | null
  courseName: string
  courseType: string
  courseDescription: string | null
  courseDuration: number | null
  learningStatus: string | null
  examStatus: string | null
  courseFee: number | null
  examFee: number | null
  certificationFee: number | null
  orderType: string
  businessLine: string
  opportunityId: string
  leadId: string
  projectName: string
  projectDescription: string | null
  startDate: string | null
  endDate: string | null
  orderAmount: number
  paidAmount: number
  refundAmount: number
  orderStatus: string
  paymentStatus: string
  managerId: string | null
  managerName: string | null
  managerPhone: string | null
  contractType: string
  contractFileUrl: string | null
  contractStatus: string
  remark: string | null
  settlementStatus: string
  settlementTime: string | null
  settlementMethod: string | null
  approvalLevel: string
  createTime: number
  updateTime: number
  contractInfo: {
    contractId: number
    contractName: string
    contractNumber: string
    startDate: number[]
    endDate: number[]
    amount: number
    status: string
    attachmentPath: string | null
    signer: string
  } | null
  paymentList: any[] | null
}

// 后端分页查询响应结构
export interface BackendPageResponse {
  code: number
  data: {
    list: BackendOrderData[]
    total: number
  }
  msg: string
}

// 新增订单请求参数
export interface AddOrderParams {
  businessOpportunity?: string
  associatedLead?: string
  orderType: string
  studentName: string
  courseName: string
  orderSource: string
  orderAmount: number
  paymentStatus: string
  learningStatus?: string
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  remark?: string
  // 合同相关字段
  contractType?: string
  contractTemplate?: string
  contractAttachment?: string
  contractNumber?: string
  contractName?: string
  signingDate?: string
  contractAmount?: string
  // 学员身份证号/唯一标识，对应数据库 student_oneid 字段
  studentOneid?: string
}

// 新增订单响应结果
export interface AddOrderResult {
  orderId: number
  orderNo: string
}

// 更新订单请求参数
export interface UpdateOrderParams {
  id: number
  businessOpportunity?: string
  associatedLead?: string
  orderType?: string
  studentName?: string
  courseName?: string
  orderSource?: string
  orderAmount?: number
  paymentStatus?: string
  learningStatus?: string
  collectionAmount?: number
  collectionMethod?: string
  collectionDate?: string
  remark?: string
  // 合同相关字段
  contractType?: string
  contractTemplate?: string
  contractAttachment?: string
  contractNumber?: string
  contractName?: string
  signingDate?: string
  contractAmount?: string
  // 学员身份证号/唯一标识，对应数据库 student_oneid 字段
  studentOneid?: string
}

// 删除订单请求参数
export interface DeleteOrderParams {
  id: number
}

// 分页查询参数
export interface OrderPageParams {
  page: number
  size: number
  orderStatus?: string
  paymentStatus?: string
  orderType?: string
  keyword?: string
  startDate?: string
  endDate?: string
}

// 分页查询结果
export interface OrderPageResult {
  records: IndividualTrainingOrder[]
  total: number
  size: number
  current: number
  pages: number
}

// 订单详情响应
export interface OrderDetailResult {
  id: number
  orderId: number
  orderNo: string
  orderType: string
  businessLine: string
  businessOpportunity?: string
  associatedLead?: string
  studentName: string
  studentPhone?: string | null
  studentEmail?: string | null
  studentOneid?: string
  courseName: string
  courseType?: string
  orderSource?: string
  orderAmount: number
  orderStatus: string
  paymentStatus: string
  learningStatus?: string
  examStatus?: string
  registrationTime?: string
  remark?: string
  // 新增字段
  opportunityId?: string
  leadId?: string
  projectName?: string
  projectDescription?: string | null
  startDate?: string | null
  endDate?: string | null
  paidAmount?: number
  refundAmount?: number
  managerId?: string | null
  managerName?: string | null
  managerPhone?: string | null
  contractType?: string
  contractFileUrl?: string | null
  contractStatus?: string
  settlementStatus?: string
  settlementTime?: string | null
  settlementMethod?: string | null
  approvalLevel?: string
  createTime?: number
  updateTime?: number
  contractInfo?: {
    contractId: number
    contractName: string
    contractNumber: string
    startDate: number[]
    endDate: number[]
    amount: number
    status: string
    attachmentPath: string | null
    signer: string
  } | null
  paymentList?: any[] | null
}

// ==================== 收款管理类型定义 ====================

// 确认收款请求参数
export interface ConfirmPaymentParams {
  orderId: number
  paymentAmount: number
  paymentType: string
  paymentTime: string
  transactionId?: string
  remark?: string
}

// 确认收款响应结果
export interface ConfirmPaymentResult {
  paymentId: number
  paymentNo: string
}

// 更新收款信息请求参数
export interface UpdatePaymentParams {
  paymentId: number
  paymentAmount?: number
  paymentType?: string
  paymentTime?: string
  transactionId?: string
  remark?: string
}

// 收款详情响应
export interface PaymentDetailResult {
  paymentId: number
  paymentNo: string
  paymentType: string
  paymentAmount: number
  paymentStatus: string
  paymentTime: string
  transactionId?: string
  operatorName: string
  remark?: string
}

// 收款列表查询参数
export interface PaymentListParams {
  orderId: number
  page?: number
  size?: number
}

// 收款列表响应
export interface PaymentListResult {
  records: PaymentDetailResult[]
  total: number
  size: number
  current: number
  pages: number
}

// ==================== 审批管理类型定义 ====================

// 提交审批请求参数
export interface SubmitApprovalParams {
  orderId: number
  approvalType: string
  approvalLevel: number
  approverIds: number[]
  remark?: string
}

// 提交审批响应结果
export interface SubmitApprovalResult {
  approvalId: number
  approvalNo: string
}

// 审批操作请求参数
export interface ApproveParams {
  approvalId: number
  orderId: number
  approvalResult: string
  approvalOpinion?: string
  nextApproverIds?: number[]
}

// 审批列表查询参数
export interface ApprovalListParams {
  orderId: number
  page?: number
  size?: number
}

// 审批记录响应
export interface ApprovalRecordResult {
  approvalId: number
  approvalNo: string
  approvalType: string
  approvalLevel: number
  approvalResult: string
  approvalOpinion?: string
  approverName: string
  approvalTime: string
}

// 审批列表响应
export interface ApprovalListResult {
  records: ApprovalRecordResult[]
  total: number
  size: number
  current: number
  pages: number
}

// ==================== 合同管理类型定义 ====================

// 确认合同请求参数
export interface ConfirmContractParams {
  orderId: number
  orderNo?: string
  contractType: string
  contractName?: string
  contractNumber?: string
  contractFileUrl?: string
  contractStatus: string
  signer?: string
  signDate?: string
  contractAmount?: string | number
  remark?: string
}

// 更新合同信息请求参数
export interface UpdateContractParams {
  orderId: number
  contractType?: string
  contractFileUrl?: string
  contractStatus?: string
  signer?: string
  signDate?: string
  remark?: string
}

// 合同信息响应
export interface ContractInfoResult {
  contractId: number
  orderId: number
  contractType: string
  contractNumber?: string
  contractName?: string
  contractAmount?: number
  contractFileUrl?: string
  contractStatus: string
  signer?: string
  signDate?: string
  createTime: string
  updateTime: string
}

// 合同上传参数
export interface ContractUploadParams {
  orderId: number
  tenantId: number
  contractFile: string
  contractName: string
  contractNumber: string
  startDate: string
  endDate: string
  amount: number
  signer: string
}

// 更新合同信息参数
export interface UpdateContractInfoParams {
  contractId: number
  orderId: number
  contractName?: string
  contractNumber?: string
  contractFile?: string
  signingDate?: string
  contractAmount?: string
  contractStatus?: string
}

// ==================== 操作日志类型定义 ====================

// 操作日志查询参数
export interface OptLogParams {
  orderNo: string
  logType?: string
  startDate?: string
  endDate?: string
  page?: number
  size?: number
}

// 操作日志记录响应
export interface OptLogRecordResult {
  id: number
  logType: string
  logTitle: string
  logContent: string
  oldStatus?: string
  newStatus?: string
  operatorName: string
  operatorRole: string
  createTime: string
}

// 操作日志列表响应
export interface OptLogListResult {
  records: OptLogRecordResult[]
  total: number
  size: number
  current: number
  pages: number
}

// ==================== 导出和统计类型定义 ====================

// 导出请求参数
export interface ExportParams {
  orderStatus?: string
  paymentStatus?: string
  orderType?: string
  keyword?: string
  startDate?: string
  endDate?: string
  exportType: string
}

// 导出响应结果
export interface ExportResult {
  downloadUrl: string
  fileName: string
}

// 统计信息响应
export interface StatisticsResult {
  totalOrders: number
  pendingOrders: number
  monthlyAmount: number
  completionRate: number
}

// ==================== API接口实现 ====================

export const IndividualTrainingOrderApi = {
  // ==================== 订单管理接口 ====================

  // 分页查询订单列表
  getOrderPage: async (params: OrderPageParams): Promise<OrderPageResult> => {
    console.log('=== 调用分页查询API ===')
    console.log('查询参数:', params)

    const response = await request.post({
      url: '/publicbiz/order-center/personal-training/page',
      data: params
    })

    console.log('=== 后端返回的原始数据 ===')
    console.log('完整响应:', response)
    console.log('响应数据类型:', typeof response)

    // 转换后端数据结构为前端期望的格式
    // 后端数据可能直接在 response 中，也可能在 response.data 中
    let backendData: any = null
    let dataList: any[] = []
    let totalCount: number = 0

    if (response && response.data && response.data.list) {
      // 数据在 response.data 中
      backendData = response.data
      dataList = response.data.list
      totalCount = response.data.total
      console.log('=== 数据在 response.data 中 ===')
    } else if (response && response.list) {
      // 数据直接在 response 中
      backendData = response
      dataList = response.list
      totalCount = response.total
      console.log('=== 数据直接在 response 中 ===')
    }

    if (backendData && dataList && dataList.length > 0) {
      console.log('=== 后端数据结构 ===')
      console.log('后端数据:', backendData)
      console.log('数据列表长度:', dataList.length)

      const convertedRecords = dataList.map((backendOrder) => {
        const converted = {
          id: backendOrder.orderId || backendOrder.id || 0, // 使用 orderId 作为 id
          orderId: backendOrder.orderId || backendOrder.id || 0, // 保留原始orderId
          orderNo: backendOrder.orderNo || backendOrder.orderNumber || '',
          orderType: backendOrder.orderType || 'training', // 默认为个人培训
          businessLine: backendOrder.businessLine || backendOrder.courseType || '-', // 优先使用businessLine，否则使用courseType
          businessOpportunity:
            backendOrder.opportunityId || backendOrder.businessOpportunity || '-', // 映射opportunityId
          associatedLead: backendOrder.leadId || backendOrder.associatedLead || '-', // 映射leadId
          studentName: backendOrder.studentName || '未知学员',
          studentPhone: backendOrder.studentPhone || '-',
          studentEmail: backendOrder.studentEmail || '-',
          studentOneid: backendOrder.studentOneid || '-',
          courseName: backendOrder.courseName || '未知课程',
          courseType: backendOrder.courseType || '-',
          orderSource: backendOrder.orderSource || '-', // 保留原始字段
          orderAmount: backendOrder.orderAmount || 0,
          orderStatus: backendOrder.orderStatus || 'draft', // 默认为草稿状态
          paymentStatus: backendOrder.paymentStatus || 'pending', // 默认为待支付
          learningStatus: backendOrder.learningStatus || 'not_started', // 默认为未开始
          examStatus: backendOrder.examStatus || 'not_registered', // 默认为未报名
          registrationTime: backendOrder.createTime
            ? new Date(backendOrder.createTime).toISOString().split('T')[0]
            : new Date().toISOString().split('T')[0], // 转换时间戳，默认为今天
          remark: backendOrder.remark || backendOrder.courseDescription || '-',
          createTime: backendOrder.createTime
            ? new Date(backendOrder.createTime).toISOString()
            : new Date().toISOString(),
          updateTime: backendOrder.updateTime
            ? new Date(backendOrder.updateTime).toISOString()
            : new Date().toISOString(),
          // 添加其他可能需要的字段
          projectName: backendOrder.projectName || '-',
          projectDescription: backendOrder.projectDescription || '-',
          startDate: backendOrder.startDate || '-',
          endDate: backendOrder.endDate || '-',
          paidAmount: backendOrder.paidAmount || 0,
          refundAmount: backendOrder.refundAmount || 0,
          managerName: backendOrder.managerName || '-',
          managerPhone: backendOrder.managerPhone || '-',
          contractType: backendOrder.contractType || 'paper', // 默认为纸质合同
          contractFileUrl: backendOrder.contractFileUrl || '-',
          contractStatus: backendOrder.contractStatus || 'unsigned', // 默认为未签署
          settlementStatus: backendOrder.settlementStatus || '-',
          settlementTime: backendOrder.settlementTime || '-',
          settlementMethod: backendOrder.settlementMethod || '-',
          approvalLevel: backendOrder.approvalLevel || '-',
          contractInfo: backendOrder.contractInfo || null
        }
        console.log('转换后的订单数据:', converted)
        return converted
      }) as IndividualTrainingOrder[]

      const result = {
        records: convertedRecords,
        total: totalCount,
        size: params.size,
        current: params.page,
        pages: Math.ceil(totalCount / params.size)
      }

      console.log('=== 转换后的结果 ===')
      console.log('最终结果:', result)
      return result
    }

    console.log('=== 数据转换失败，返回空结果 ===')
    // 如果转换失败，返回空结果
    return {
      records: [],
      total: 0,
      size: params.size,
      current: params.page,
      pages: 0
    }
  },

  // 新增订单
  addOrder: async (data: AddOrderParams): Promise<AddOrderResult> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/add',
      data
    })
  },

  // 更新订单
  updateOrder: async (data: UpdateOrderParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/update',
      data
    })
  },

  // 删除订单
  deleteOrder: async (data: DeleteOrderParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/delete',
      data
    })
  },

  // 根据订单号获取订单详情
  getOrderByOrderNo: async (orderNo: string): Promise<OrderDetailResult> => {
    return await request.get({
      url: `/publicbiz/order-center/personal-training/getByOrderNo/${orderNo}`
    })
  },

  // ==================== 收款管理接口 ====================

  // 确认收款 - 根据接口文档更新
  confirmCollection: async (data: {
    orderId: number
    orderNo: string
    collectionAmount: number
    collectionMethod: string
    collectionDate: string
    operatorName: string
    collectionRemark?: string
    transactionId?: string
    bankAccount?: string
    bankName?: string
  }): Promise<any> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/confirm-collection',
      data
    })
  },

  // 更新收款信息 - 根据接口文档更新
  updateCollection: async (data: {
    orderId: number
    orderNo: string
    collectionAmount: number
    collectionMethod: string
    collectionDate: string
    operatorName: string
    collectionRemark?: string
    transactionId?: string
    bankAccount?: string
    bankName?: string
  }): Promise<any> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/update-collection',
      data
    })
  },

  // 确认收款（保持向后兼容）
  confirmPayment: async (data: ConfirmPaymentParams): Promise<ConfirmPaymentResult> => {
    // 转换为新的接口格式
    const collectionData = {
      orderId: data.orderId,
      orderNo: '', // 需要从订单数据中获取
      collectionAmount: data.paymentAmount,
      collectionMethod: data.paymentType,
      collectionDate: data.paymentTime,
      operatorName: '当前用户', // TODO: 从用户信息中获取
      collectionRemark: data.remark,
      transactionId: data.transactionId
    }

    return await request.post({
      url: '/publicbiz/order-center/personal-training/confirm-collection',
      data: collectionData
    })
  },

  // 更新收款信息（保持向后兼容）
  updatePayment: async (data: UpdatePaymentParams): Promise<void> => {
    // 转换为新的接口格式
    const collectionData = {
      orderId: 0, // 需要从paymentId中获取
      orderNo: '', // 需要从订单数据中获取
      collectionAmount: data.paymentAmount || 0,
      collectionMethod: data.paymentType || '',
      collectionDate: data.paymentTime || new Date().toISOString().split('T')[0],
      operatorName: '当前用户', // TODO: 从用户信息中获取
      collectionRemark: data.remark
    }

    return await request.post({
      url: '/publicbiz/order-center/personal-training/update-collection',
      data: collectionData
    })
  },

  // 获取收款详情
  getPaymentDetail: async (orderId: number): Promise<PaymentDetailResult> => {
    return await request.get({
      url: `/publicbiz/order-center/personal-training/getPaymentDetail/${orderId}`
    })
  },

  // 获取收款列表
  getPaymentList: async (params: PaymentListParams): Promise<PaymentListResult> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/getPaymentList',
      data: params
    })
  },

  // ==================== 审批管理接口 ====================

  // 发起审批 - 根据接口文档更新
  submitApproval: async (data: {
    orderId: number
    approvalType: string
    approvalLevel: number
    approverIds: number[]
    approvalOpinion?: string
    nextApproverIds?: number[]
    remark?: string
    operatorName?: string
  }): Promise<any> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/submit-approval',
      data
    })
  },

  // 审批通过 - 根据接口文档更新
  approve: async (data: {
    orderId: number
    approvalType: string
    approvalLevel: number
    approverIds: number[]
    approvalResult: string
    approvalOpinion?: string
    operatorName?: string
  }): Promise<any> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/approve',
      data
    })
  },

  // 审批拒绝 - 根据接口文档更新
  reject: async (data: {
    orderId: number
    approvalType: string
    approvalLevel: number
    approverIds: number[]
    approvalResult: string
    approvalOpinion?: string
    rejectReason: string
    operatorName?: string
  }): Promise<any> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/reject',
      data
    })
  },

  // 审批操作（保持向后兼容）
  approveOrder: async (data: ApproveParams): Promise<void> => {
    // 转换为新的接口格式
    const approvalData = {
      orderId: data.orderId,
      approvalType: 'training_approval', // 默认审批类型
      approvalLevel: 1, // 默认审批级别
      approverIds: [data.approvalId], // 使用approvalId作为审批人ID
      approvalResult: data.approvalResult,
      approvalOpinion: data.approvalOpinion,
      operatorName: '当前用户' // TODO: 从用户信息中获取
    }

    if (data.approvalResult === 'rejected') {
      return await request.post({
        url: '/publicbiz/order-center/personal-training/reject',
        data: { ...approvalData, rejectReason: data.approvalOpinion || '审批拒绝' }
      })
    } else {
      return await request.post({
        url: '/publicbiz/order-center/personal-training/approve',
        data: approvalData
      })
    }
  },

  // 获取审批列表
  getApprovalList: async (params: {
    orderId: number
    page?: number
    size?: number
    approvalType?: string
    approvalResult?: string
    startDate?: string
    endDate?: string
  }): Promise<ApprovalListResult> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/approval-page',
      data: params
    })
  },

  // ==================== 合同管理接口 ====================

  // 确认合同
  confirmContract: async (data: ConfirmContractParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/confirm-contract',
      data
    })
  },

  // 更新合同信息
  updateContract: async (data: UpdateContractParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/update-contract',
      data
    })
  },

  // 获取合同信息
  getContractInfo: async (orderId: number): Promise<ContractInfoResult> => {
    const response = await request.get({
      url: `/publicbiz/order-center/personal-training/get-contract-info/${orderId}`
    })
    // 根据接口返回格式，数据在response.data中
    return response.data || response
  },

  // 上传合同
  uploadContract: async (
    data: ContractUploadParams
  ): Promise<{ contractId: number; success: boolean; message: string }> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/upload-contract',
      data
    })
  },

  // 更新合同信息
  updateContractInfo: async (data: UpdateContractInfoParams): Promise<void> => {
    return await request.post({
      url: '/admin-api/publicbiz/order-center/personal-training/update-contract-info',
      data
    })
  },

  // ==================== 操作日志接口 ====================

  // 获取操作日志列表
  getOptLogList: async (params: OptLogParams): Promise<OptLogListResult> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/opt-log-page',
      data: params
    })
  },

  // ==================== 导出接口 ====================

  // 导出订单列表
  exportOrders: async (params: ExportParams): Promise<ExportResult> => {
    return await request.post({
      url: '/publicbiz/order-center/personal-training/export',
      data: params
    })
  },

  // ==================== 统计接口 ====================

  // 获取订单统计信息
  getStatistics: async (): Promise<StatisticsResult> => {
    return await request.get({
      url: '/publicbiz/order-center/personal-training/getStatistics'
    })
  }
}

// ==================== 数据字典常量 ====================

// 订单类型
export const ORDER_TYPE = {
  TRAINING: 'training',
  CERTIFICATION: 'certification'
} as const

// 订单状态
export const ORDER_STATUS = {
  DRAFT: 'draft',
  PENDING_APPROVAL: 'pending_approval',
  APPROVING: 'approving',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  PENDING_PAYMENT: 'pending_payment',
  EXECUTING: 'executing',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

// 支付状态
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  REFUNDED: 'refunded',
  CANCELLED: 'cancelled'
} as const

// 学习状态
export const LEARNING_STATUS = {
  NOT_STARTED: 'not_started',
  LEARNING: 'learning',
  COMPLETED: 'completed'
} as const

// 考试状态
export const EXAM_STATUS = {
  NOT_REGISTERED: 'not_registered',
  REGISTERED: 'registered',
  PASSED: 'passed',
  FAILED: 'failed'
} as const

// 合同状态
export const CONTRACT_STATUS = {
  UNSIGNED: 'unsigned',
  SIGNED: 'signed',
  REJECTED: 'rejected'
} as const

// 支付类型
export const PAYMENT_TYPE = {
  CASH: 'cash',
  WECHAT: 'wechat',
  ALIPAY: 'alipay',
  BANK_TRANSFER: 'bank_transfer',
  POS: 'pos',
  OTHER: 'other'
} as const

// 合同类型
export const CONTRACT_TYPE = {
  ELECTRONIC: 'electronic',
  PAPER: 'paper'
} as const

// 订单来源（与线索来源保持一致）
export const ORDER_SOURCE = {
  ONLINE_MINIPROGRAM: '1', // 官网注册
  OFFLINE_REGISTRATION: '2', // 市场活动
  PHONE_CONSULTATION: '7', // 电话营销
  FRIEND_RECOMMENDATION: '6', // 客户推荐
  WECHAT_ARTICLE: '3', // 公众号文章
  VIDEO_ACCOUNT: '4', // 视频号
  DOUYIN: '5', // 抖音
  SOCIAL_MEDIA: '8', // 社交媒体
  EXHIBITION: '9', // 展会
  OTHER: '99' // 其他
} as const

// 订单来源到线索来源的映射（保持向后兼容）
export const ORDER_SOURCE_LEGACY = {
  ONLINE_MINIPROGRAM: 'online_miniprogram',
  OFFLINE_REGISTRATION: 'offline_registration',
  PHONE_CONSULTATION: 'phone_consultation',
  FRIEND_RECOMMENDATION: 'friend_recommendation'
} as const

// 导出类型
export const EXPORT_TYPE = {
  EXCEL: 'excel'
} as const

// ==================== 状态映射工具函数 ====================

// 获取订单类型显示文本
export const getOrderTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [ORDER_TYPE.TRAINING]: '个人培训',
    [ORDER_TYPE.CERTIFICATION]: '考试认证'
  }
  return typeMap[type] || '未知'
}

// 获取订单状态显示文本
export const getOrderStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [ORDER_STATUS.DRAFT]: '草稿',
    [ORDER_STATUS.PENDING_APPROVAL]: '待审批',
    [ORDER_STATUS.APPROVING]: '审批中',
    [ORDER_STATUS.APPROVED]: '已批准',
    [ORDER_STATUS.REJECTED]: '已拒绝',
    [ORDER_STATUS.PENDING_PAYMENT]: '待支付',
    [ORDER_STATUS.EXECUTING]: '执行中',
    [ORDER_STATUS.COMPLETED]: '已完成',
    [ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取支付状态显示文本
export const getPaymentStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [PAYMENT_STATUS.PENDING]: '待支付',
    [PAYMENT_STATUS.PAID]: '已支付',
    [PAYMENT_STATUS.REFUNDED]: '已退款',
    [PAYMENT_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取学习状态显示文本
export const getLearningStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [LEARNING_STATUS.NOT_STARTED]: '未开始',
    [LEARNING_STATUS.LEARNING]: '学习中',
    [LEARNING_STATUS.COMPLETED]: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取考试状态显示文本
export const getExamStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [EXAM_STATUS.NOT_REGISTERED]: '未报名',
    [EXAM_STATUS.REGISTERED]: '已报名',
    [EXAM_STATUS.PASSED]: '已通过',
    [EXAM_STATUS.FAILED]: '未通过'
  }
  return statusMap[status] || '未知'
}

// 获取合同状态显示文本
export const getContractStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [CONTRACT_STATUS.UNSIGNED]: '未签署',
    [CONTRACT_STATUS.SIGNED]: '已签署',
    [CONTRACT_STATUS.REJECTED]: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取支付类型显示文本
export const getPaymentTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [PAYMENT_TYPE.CASH]: '现金',
    [PAYMENT_TYPE.WECHAT]: '微信支付',
    [PAYMENT_TYPE.ALIPAY]: '支付宝',
    [PAYMENT_TYPE.BANK_TRANSFER]: '银行转账',
    [PAYMENT_TYPE.POS]: 'POS机刷卡',
    [PAYMENT_TYPE.OTHER]: '其他'
  }
  return typeMap[type] || '未知'
}

// 获取合同类型显示文本
export const getContractTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [CONTRACT_TYPE.ELECTRONIC]: '电子合同',
    [CONTRACT_TYPE.PAPER]: '纸质合同'
  }
  return typeMap[type] || '未知'
}

// 获取订单来源显示文本
export const getOrderSourceText = async (source: string): Promise<string> => {
  try {
    // 从线索管理接口获取线索来源选项
    const leadSourceOptions = await getLeadSourceOptions()
    const sourceOption = leadSourceOptions.find((option) => option.value === source)
    return sourceOption ? sourceOption.label : '未知'
  } catch (error) {
    console.error('获取线索来源失败:', error)
    // 降级到本地映射
    const sourceMap: Record<string, string> = {
      [ORDER_SOURCE.ONLINE_MINIPROGRAM]: '线上小程序',
      [ORDER_SOURCE.OFFLINE_REGISTRATION]: '线下报名',
      [ORDER_SOURCE.PHONE_CONSULTATION]: '电话咨询',
      [ORDER_SOURCE.FRIEND_RECOMMENDATION]: '朋友推荐'
    }
    return sourceMap[source] || '未知'
  }
}

// 获取线索来源选项（从线索管理接口）
export const getLeadSourceOptions = async (): Promise<Array<{ label: string; value: string }>> => {
  try {
    // 尝试从线索管理接口获取线索来源选项
    const { ClueCenterApi } = await import('@/api/infra/clueCenter')
    // 由于线索管理API中没有getLeadSourceOptions方法，我们使用默认选项
    // 在实际项目中，可以通过其他接口获取线索来源数据
    return getDefaultLeadSourceOptions()
  } catch (error) {
    console.error('获取线索来源选项失败:', error)
    // 返回默认选项
    return getDefaultLeadSourceOptions()
  }
}

// 获取默认线索来源选项
export const getDefaultLeadSourceOptions = (): Array<{ label: string; value: string }> => {
  return [
    { label: '官网注册', value: '1' },
    { label: '市场活动', value: '2' },
    { label: '公众号文章', value: '3' },
    { label: '视频号', value: '4' },
    { label: '抖音', value: '5' },
    { label: '客户推荐', value: '6' },
    { label: '电话营销', value: '7' },
    { label: '社交媒体', value: '8' },
    { label: '展会', value: '9' },
    { label: '其他', value: '99' }
  ]
}

// 同步版本的获取订单来源显示文本（保持向后兼容）
export const getOrderSourceTextSync = (source: string): string => {
  const sourceMap: Record<string, string> = {
    [ORDER_SOURCE.ONLINE_MINIPROGRAM]: '官网注册',
    [ORDER_SOURCE.OFFLINE_REGISTRATION]: '市场活动',
    [ORDER_SOURCE.PHONE_CONSULTATION]: '电话营销',
    [ORDER_SOURCE.FRIEND_RECOMMENDATION]: '客户推荐',
    [ORDER_SOURCE.WECHAT_ARTICLE]: '公众号文章',
    [ORDER_SOURCE.VIDEO_ACCOUNT]: '视频号',
    [ORDER_SOURCE.DOUYIN]: '抖音',
    [ORDER_SOURCE.SOCIAL_MEDIA]: '社交媒体',
    [ORDER_SOURCE.EXHIBITION]: '展会',
    [ORDER_SOURCE.OTHER]: '其他'
  }
  return sourceMap[source] || '未知'
}

// ==================== 状态标签类型映射 ====================

// 获取订单类型标签类型
export const getOrderTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    [ORDER_TYPE.TRAINING]: 'primary',
    [ORDER_TYPE.CERTIFICATION]: 'success'
  }
  return typeMap[type] || 'info'
}

// 获取订单状态标签类型
export const getOrderStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [ORDER_STATUS.DRAFT]: 'info',
    [ORDER_STATUS.PENDING_APPROVAL]: 'warning',
    [ORDER_STATUS.APPROVING]: 'warning',
    [ORDER_STATUS.APPROVED]: 'success',
    [ORDER_STATUS.REJECTED]: 'danger',
    [ORDER_STATUS.PENDING_PAYMENT]: 'warning',
    [ORDER_STATUS.EXECUTING]: 'primary',
    [ORDER_STATUS.COMPLETED]: 'success',
    [ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态标签类型
export const getPaymentStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [PAYMENT_STATUS.PENDING]: 'warning',
    [PAYMENT_STATUS.PAID]: 'success',
    [PAYMENT_STATUS.REFUNDED]: 'info',
    [PAYMENT_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取学习状态标签类型
export const getLearningStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [LEARNING_STATUS.NOT_STARTED]: 'info',
    [LEARNING_STATUS.LEARNING]: 'primary',
    [LEARNING_STATUS.COMPLETED]: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取考试状态标签类型
export const getExamStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [EXAM_STATUS.NOT_REGISTERED]: 'info',
    [EXAM_STATUS.REGISTERED]: 'warning',
    [EXAM_STATUS.PASSED]: 'success',
    [EXAM_STATUS.FAILED]: 'danger'
  }
  return statusMap[status] || 'info'
}

// ==================== 默认值常量 ====================

// 默认分页参数
export const DEFAULT_PAGE_PARAMS = {
  page: 1,
  size: 10
} as const

// 默认搜索参数
export const DEFAULT_SEARCH_PARAMS = {
  orderStatus: '',
  paymentStatus: '',
  orderType: '',
  keyword: '',
  startDate: '',
  endDate: ''
} as const

// 默认订单状态
export const DEFAULT_ORDER_STATUS = ORDER_STATUS.DRAFT

// 默认支付状态
export const DEFAULT_PAYMENT_STATUS = PAYMENT_STATUS.PENDING

// 默认学习状态
export const DEFAULT_LEARNING_STATUS = LEARNING_STATUS.NOT_STARTED

// 默认考试状态
export const DEFAULT_EXAM_STATUS = EXAM_STATUS.NOT_REGISTERED

// ==================== 验证规则常量 ====================

// 订单金额验证规则
export const ORDER_AMOUNT_RULES = {
  required: true,
  min: 0.01,
  max: 999999.99,
  message: '订单金额必须在0.01-999999.99之间'
} as const

// 学员姓名验证规则
export const STUDENT_NAME_RULES = {
  required: true,
  min: 2,
  max: 20,
  message: '学员姓名长度必须在2-20个字符之间'
} as const

// 课程名称验证规则
export const COURSE_NAME_RULES = {
  required: true,
  min: 2,
  max: 100,
  message: '课程名称长度必须在2-100个字符之间'
} as const

// ==================== 导出默认接口 ====================

export default IndividualTrainingOrderApi
