<!--
  页面名称：个人培训与认证订单详情
  功能描述：查看个人培训与认证订单详情，支持数据展示、操作日志查看、审批、合同上传等功能
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="个人培训订单详情"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
    :append-to-body="true"
    :modal="true"
    :z-index="1000"
  >
    <div class="drawer-content" v-loading="loading">
      <!-- 课程标题横幅 -->
      <div class="course-banner">
        <div class="course-title">{{ mergedOrderData?.courseName || '项目管理PMP认证课程' }}</div>
        <el-tag type="primary" size="small">{{
          getOrderTypeText(mergedOrderData?.orderType)
        }}</el-tag>
      </div>

      <!-- 订单基本信息 -->
      <div class="info-section">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{
            mergedOrderData?.orderNo || mergedOrderData?.orderNumber || 'PT202406001'
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">学员姓名：</span>
          <span class="value">{{ mergedOrderData?.studentName || '王小明' }}</span>
        </div>
        <div class="info-item">
          <span class="label">课程/考试项目：</span>
          <span class="value">{{ mergedOrderData?.courseName || '项目管理PMP认证课程' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单来源：</span>
          <span class="value">
            {{ mergedOrderData?.orderSource || '-' }}
            <el-tag
              v-if="mergedOrderData?.orderSource"
              type="info"
              size="small"
              style="margin-left: 8px"
            >
              已绑定
            </el-tag>
          </span>
        </div>
        <div class="info-item">
          <span class="label">订单金额：</span>
          <span class="value amount">¥{{ mergedOrderData?.orderAmount || '4,500' }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单状态：</span>
          <el-tag :type="getOrderStatusTagType(mergedOrderData?.orderStatus) as any" size="small">
            {{ getOrderStatusText(mergedOrderData?.orderStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">支付状态：</span>
          <el-tag
            :type="getPaymentStatusTagType(mergedOrderData?.paymentStatus) as any"
            size="small"
          >
            {{ getPaymentStatusText(mergedOrderData?.paymentStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">学习/考试状态：</span>
          <el-tag
            :type="getLearningStatusTagType(mergedOrderData?.learningStatus) as any"
            size="small"
          >
            {{ getLearningStatusText(mergedOrderData?.learningStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">报名时间：</span>
          <span class="value">{{
            mergedOrderData?.createTime
              ? new Date(mergedOrderData.createTime).toLocaleDateString()
              : '2024-06-10'
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联商机：</span>
          <span class="value">{{
            mergedOrderData?.opportunityId || mergedOrderData?.businessOpportunity || 'OPP202406009'
          }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联线索：</span>
          <span class="value">{{
            mergedOrderData?.leadId || mergedOrderData?.lead || 'LEAD202406009'
          }}</span>
        </div>
      </div>

      <!-- 收款信息模块 -->
      <div v-if="hasPaymentInfo" class="detail-module payment-info">
        <div class="module-header">
          <el-icon><Money /></el-icon>
          <span class="module-title">收款信息</span>
        </div>
        <div class="module-content">
          <div class="info-item">
            <span class="label">收款金额：</span>
            <span class="value amount"
              >¥{{ paymentInfo.actualAmount || mergedOrderData?.orderAmount || '0' }}</span
            >
          </div>
          <div class="info-item">
            <span class="label">收款方式：</span>
            <span class="value">{{ getPaymentMethodText(paymentInfo.paymentMethod) || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款日期：</span>
            <span class="value">{{ paymentInfo.paymentDate || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">操作人：</span>
            <span class="value">{{ paymentInfo.operator || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款备注：</span>
            <span class="value">{{ paymentInfo.paymentNotes || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 合同信息模块 -->
      <div class="detail-module">
        <div class="module-header">
          <el-icon><Document /></el-icon>
          <span class="module-title">合同信息</span>
        </div>

        <!-- 合同详细信息 -->
        <div
          v-if="contractInfo.contractFile || contractInfo.contractFileUrl"
          class="module-content"
        >
          <div class="info-item">
            <span class="label">合同类型：</span>
            <span class="value">{{
              getContractTypeText(contractInfo.contractType || orderData?.contractType)
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">合同编号：</span>
            <span class="value">{{
              contractInfo.contractNumber || orderData?.orderNumber || '-'
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">合同名称：</span>
            <span class="value">{{
              contractInfo.contractName || orderData?.courseName || '-'
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">签署日期：</span>
            <span class="value">{{
              contractInfo.signingDate || orderData?.createTime || '-'
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">合同金额：</span>
            <span class="value amount"
              >¥{{
                (contractInfo.contractAmount || orderData?.orderAmount)?.toLocaleString() || '0'
              }}</span
            >
          </div>
          <div class="info-item">
            <span class="label">合同附件：</span>
            <div class="file-item" v-if="contractInfo.contractFile || contractInfo.contractFileUrl">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{
                getFileNameFromUrl(contractInfo.contractFile || contractInfo.contractFileUrl)
              }}</span>
              <el-button size="small" type="info" @click="downloadContract">下载</el-button>
            </div>
            <span v-else class="value">暂无合同附件</span>
          </div>
          <div class="info-item">
            <span class="label">合同状态：</span>
            <span class="value">
              <el-tag
                :type="
                  getContractStatusTagType(
                    contractInfo.contractStatus || mergedOrderData?.contractStatus || 'pending'
                  )
                "
                size="small"
              >
                {{
                  getContractStatusText(
                    contractInfo.contractStatus || mergedOrderData?.contractStatus || 'pending'
                  )
                }}
              </el-tag>
              <el-tag
                v-if="contractInfo.contractStatus || mergedOrderData?.contractStatus"
                type="success"
                size="small"
                style="margin-left: 8px"
              >
                已绑定
              </el-tag>
            </span>
          </div>
        </div>

        <!-- 合同操作按钮 -->
        <div class="contract-actions">
          <!-- 当没有合同时显示上传按钮 -->
          <div
            v-if="!contractInfo.contractFile && !contractInfo.contractFileUrl"
            class="upload-actions"
            style="text-align: center; margin: 20px 0"
          >
            <el-button type="primary" @click="showElectronicContractDialog">
              <el-icon><Document /></el-icon>
              发起电子合同
            </el-button>
            <el-button type="info" @click="showContractUploadDialog">
              <el-icon><Upload /></el-icon>
              上传纸质合同
            </el-button>
          </div>

          <!-- 当有合同时显示管理按钮 -->
          <div v-if="contractInfo.contractFile || contractInfo.contractFileUrl">
            <el-button size="small" type="primary" @click="viewContract">
              <el-icon><View /></el-icon>
              查看合同
            </el-button>
            <el-button size="small" type="success" @click="downloadContract">
              <el-icon><Download /></el-icon>
              下载合同
            </el-button>
            <el-button size="small" type="warning" @click="editContract">
              <el-icon><Edit /></el-icon>
              编辑合同
            </el-button>
          </div>
        </div>
      </div>

      <!-- 审批流程模块 -->
      <div class="detail-module">
        <div class="module-header">
          <el-icon><Tools /></el-icon>
          <span class="module-title">审批流程</span>
          <el-button
            v-if="shouldShowApprovalButton"
            type="primary"
            size="small"
            @click="showApprovalDialog"
          >
            发起审批
          </el-button>
        </div>
        <div class="module-content">
          <!-- 审批列表组件 -->
          <ApprovalList
            :order-id="mergedOrderData?.id || orderData?.id"
            :order-no="mergedOrderData?.orderNo || orderData?.orderNo"
            :order-data="mergedOrderData || orderData"
            @approval-status-change="handleApprovalStatusChange"
          />
        </div>
      </div>

      <!-- 操作日志模块 -->
      <div class="detail-module">
        <div class="module-header">
          <el-icon><Refresh /></el-icon>
          <span class="module-title">操作日志</span>
          <el-button type="text" size="small" class="view-log-btn" @click="onViewOptLog">
            <el-icon><List /></el-icon>
            查看完整日志
          </el-button>
        </div>
        <div class="module-content">
          <div class="log-list">
            <div v-if="recentLogs.length === 0" class="no-logs">
              <el-empty description="暂无操作日志" :image-size="60" />
            </div>
            <div v-else v-for="log in recentLogs" :key="log.id" class="log-item">
              <div class="log-dot"></div>
              <div class="log-content">
                <div class="log-time">{{ formatDateTime(log.createTime || '') }}</div>
                <div class="log-message">
                  <span class="operator-name">{{ log.operatorName || '系统' }}</span>
                  <span class="log-action">{{ log.logTitle || log.logType || '操作' }}</span>
                </div>
                <div v-if="log.logContent && log.logType !== '订单创建'" class="log-details">
                  <div class="log-content-text">
                    {{ log.logContent }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <!-- 确认收款按钮 -->
        <div class="collection-actions" v-if="shouldShowCollectionButton">
          <el-button type="success" @click="onConfirmPayment">
            <el-icon><Money /></el-icon>
            确认收款
          </el-button>

          <!-- 临时调试信息 -->
          <div
            v-if="hasPaymentInfo"
            class="debug-info"
            style="
              margin-top: 10px;
              padding: 10px;
              background: #f5f7fa;
              border-radius: 4px;
              font-size: 12px;
              color: #666;
            "
          >
            <div><strong>调试信息（收款按钮测试）:</strong></div>
            <div>订单状态: {{ mergedOrderData?.orderStatus || '未知' }}</div>
            <div>支付状态: {{ mergedOrderData?.paymentStatus || '未知' }}</div>
            <div>合同状态: {{ contractInfo.contractStatus || '未知' }}</div>
            <div>已收款金额: {{ paymentInfo.actualAmount || '0' }}</div>
            <div>订单金额: {{ mergedOrderData?.orderAmount || '未知' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 操作日志抽屉 -->
  <OptLog v-model:visible="optLogVisible" :order-data="mergedOrderData || orderData" />

  <!-- 收款确认对话框 -->
  <PaymentConfirmDialog
    v-model:visible="paymentConfirmVisible"
    :order-data="mergedOrderData"
    @confirm="onPaymentConfirm"
  />

  <!-- 审批弹窗 -->
  <OrderApprovalDialog
    v-model:visible="approvalVisible"
    :order-data="orderData"
    @success="handleApprovalSuccess"
  />

  <!-- 合同上传弹窗 -->
  <ContractUploadDialog
    v-model:visible="contractUploadVisible"
    :order-data="mergedOrderData"
    :is-edit="isEditContract"
    :existing-contract="mergedOrderData?.contractInfo"
    @success="handleContractUploadSuccess"
  />
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import {
  Money,
  Document,
  Tools,
  Refresh,
  List,
  Upload,
  Download,
  View,
  Edit
} from '@element-plus/icons-vue'
import OptLog from './OptLog.vue'
import PaymentConfirmDialog from './PaymentConfirmDialog.vue'
import OrderApprovalDialog from './OrderApprovalDialog.vue'
import ContractUploadDialog from './ContractUploadDialog.vue'

import { ElMessage } from 'element-plus'
import {
  IndividualTrainingOrderApi,
  getOrderTypeText,
  getOrderStatusText,
  getPaymentStatusText,
  getLearningStatusText,
  getOrderTypeTagType,
  getOrderStatusTagType,
  getPaymentStatusTagType,
  getLearningStatusTagType
} from '@/api/OrderCenter/IndividualtrainingOrder'
import ApprovalList from './ApprovalList.vue'

// Props
interface Props {
  visible: boolean
  orderData?: any
  orderNo?: string // 订单号，用于接口调用
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [data: any]
  'payment-confirmed': [paymentData: any]
  'order-status-updated': [orderData: any]
}>()

// 响应式数据
/** 操作日志抽屉显示状态 */
const optLogVisible = ref(false)
/** 收款确认对话框显示状态 */
const paymentConfirmVisible = ref(false)
/** 审批弹窗显示状态 */
const approvalVisible = ref(false)
/** 合同上传弹窗显示状态 */
const contractUploadVisible = ref(false)

/** 是否为编辑合同模式 */
const isEditContract = ref(false)

/** 收款信息数据 */
const paymentInfo = ref({
  actualAmount: '',
  paymentMethod: '',
  paymentDate: '',
  operator: '',
  paymentNotes: ''
})
/** 合同信息数据 */
const contractInfo = ref({
  contractName: '',
  contractFile: '',
  contractFileUrl: '',
  contractType: '',
  contractStatus: 'pending', // 'pending', 'approved', 'rejected'
  contractNumber: '',
  signingDate: '',
  contractAmount: ''
})
/** 审批列表 */
const approvalList = ref<any[]>([])
/** 加载状态 */
const loading = ref(false)
/** 接口获取的订单数据 */
const apiOrderData = ref<any>(null)
/** 最近操作日志 */
const recentLogs = ref<any[]>([])

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 合并后的订单数据，优先使用接口数据 */
const mergedOrderData = computed(() => {
  return apiOrderData.value || props.orderData
})

/** 是否显示审批按钮 */
const shouldShowApprovalButton = computed(() => {
  const status = props.orderData?.orderStatus
  const contractStatus = contractInfo.value.contractStatus

  // 草稿状态、待审批状态、审批驳回状态都可以发起审批
  // 如果有合同文件，则合同状态为待审批时也可以发起审批
  return (
    status === 'draft' ||
    status === 'pending_approval' ||
    status === 'rejected' ||
    (contractInfo.value.contractFile && contractStatus === 'pending')
  )
})

/** 是否显示收款按钮 */
const shouldShowCollectionButton = computed(() => {
  const status = mergedOrderData.value?.orderStatus
  const paymentStatus = mergedOrderData.value?.paymentStatus
  const contractStatus = contractInfo.value.contractStatus

  console.log('=== 收款按钮显示逻辑检查 ===')
  console.log('订单状态:', status)
  console.log('支付状态:', paymentStatus)
  console.log('合同状态:', contractStatus)
  console.log('已收款金额:', paymentInfo.value.actualAmount)

  // 如果支付状态已经是"已支付"，则隐藏收款按钮
  if (paymentStatus === 'paid') {
    console.log('支付状态为已支付，隐藏收款按钮')
    return false
  }

  // 如果已经收款，则隐藏收款按钮
  if (paymentInfo.value.actualAmount) {
    console.log('已有收款记录，隐藏收款按钮')
    return false
  }

  // 订单状态为待付款、支付状态为待支付、且合同已批准时显示收款按钮
  const shouldShow =
    status === 'pending_payment' &&
    paymentStatus === 'pending' &&
    (contractStatus === 'approved' || contractStatus === 'signed')

  console.log('收款按钮显示结果:', shouldShow)
  return shouldShow
})

/** 是否显示收款信息模块 */
const hasPaymentInfo = computed(() => {
  // 检查是否有实际的收款信息
  const hasActualPayment =
    paymentInfo.value.actualAmount &&
    paymentInfo.value.paymentMethod &&
    paymentInfo.value.paymentDate

  console.log('=== 收款信息显示检查 ===')
  console.log('已收款金额:', paymentInfo.value.actualAmount)
  console.log('收款方式:', paymentInfo.value.paymentMethod)
  console.log('收款日期:', paymentInfo.value.paymentDate)
  console.log('是否有实际收款:', hasActualPayment)

  // 只有有实际收款信息时才显示
  return hasActualPayment
})

/** 获取订单详情数据 */
const fetchOrderDetail = async () => {
  if (!props.orderNo) {
    console.log('没有订单号，跳过接口调用')
    return
  }

  try {
    loading.value = true
    console.log('开始获取订单详情，订单号:', props.orderNo)

    // 使用API接口获取订单详情
    const result = await IndividualTrainingOrderApi.getOrderByOrderNo(props.orderNo)

    // 由于响应拦截器已经处理了错误情况，这里直接使用返回的数据
    apiOrderData.value = result
    console.log('接口获取订单详情成功:', result)
    console.log('订单来源:', result.orderSource)
    console.log('合同状态:', result.contractStatus)
    console.log('合同信息:', result.contractInfo)

    // 更新支付信息
    if (result.paymentList && result.paymentList.length > 0) {
      const payment = result.paymentList[0]
      paymentInfo.value = {
        actualAmount: payment.paymentAmount?.toString() || '',
        paymentMethod: payment.paymentType || '',
        paymentDate: payment.paymentTime
          ? new Date(payment.paymentTime).toISOString().slice(0, 19).replace('T', ' ')
          : '',
        operator: payment.operatorName || '',
        paymentNotes: payment.paymentRemark || ''
      }
    } else {
      // 没有支付信息时，清空相关字段
      paymentInfo.value = {
        actualAmount: '',
        paymentMethod: '',
        paymentDate: '',
        operator: '',
        paymentNotes: ''
      }
    }

    // 更新合同信息
    if (result.contractInfo || result.contractStatus || result.contractType) {
      contractInfo.value = {
        contractName: result.contractInfo?.contractName || '',
        contractFile: result.contractInfo?.attachmentPath || '',
        contractFileUrl: result.contractFileUrl || result.contractInfo?.attachmentPath || '',
        contractType: result.contractType || 'paper',
        contractStatus: result.contractStatus || result.contractInfo?.status || 'pending',
        contractNumber: result.contractInfo?.contractNumber || '',
        signingDate: result.contractInfo?.startDate
          ? `${result.contractInfo.startDate[0]}-${String(result.contractInfo.startDate[1]).padStart(2, '0')}-${String(result.contractInfo.startDate[2]).padStart(2, '0')}`
          : '',
        contractAmount:
          result.contractInfo?.amount?.toString() || result.orderAmount?.toString() || ''
      }
    }

    // 如果订单来源为空，尝试从线索接口获取
    if (!result.orderSource && result.leadId) {
      await fetchLeadSource(result.leadId)
    }

    ElMessage.success('订单详情获取成功')

    // 初始化审批列表
    await initApprovalList()
    // 初始化最近操作日志
    await initRecentLogs()
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.warning('获取订单详情失败，使用传入数据')
  } finally {
    loading.value = false
  }
}

// 方法
/** 处理关闭 */
const handleClose = () => {
  visible.value = false
}

/** 处理编辑 */
const handleEdit = () => {
  emit('edit', mergedOrderData.value)
  visible.value = false
}

/** 查看操作日志 */
const onViewOptLog = () => {
  console.log('=== 查看页面: 点击查看完整日志 ===')
  console.log('mergedOrderData:', mergedOrderData.value)
  console.log('orderData:', props.orderData)
  console.log('传递的订单数据:', mergedOrderData.value || props.orderData)

  optLogVisible.value = true
}

/** 确认收款 */
const onConfirmPayment = () => {
  console.log('=== 点击确认收款按钮 ===')
  console.log('当前订单数据:', mergedOrderData.value)
  console.log('当前支付信息:', paymentInfo.value)
  console.log('当前合同信息:', contractInfo.value)

  paymentConfirmVisible.value = true
}

/** 获取合同类型文本 */
const getContractTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    electronic: '电子合同',
    paper: '纸质合同'
  }
  return typeMap[type] || '未知'
}

/** 获取合同状态文本 */
const getContractStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消',
    unsigned: '未签署',
    signed: '已签署'
  }
  return statusMap[status] || '未知'
}

/** 获取合同状态标签类型 */
const getContractStatusTagType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info',
    unsigned: 'info',
    signed: 'success'
  }
  return typeMap[status] || 'info'
}

/** 从URL获取文件名 */
const getFileNameFromUrl = (url: string): string => {
  if (!url) return ''
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    return pathname.split('/').pop() || '未知文件'
  } catch {
    // 如果不是有效的URL，尝试直接提取文件名
    return url.split('/').pop() || url || '未知文件'
  }
}

/** 显示电子合同对话框 */
const showElectronicContractDialog = () => {
  // TODO: 实现电子合同功能
  ElMessage.info('电子合同功能开发中...')
}

/** 显示合同上传弹窗 */
const showContractUploadDialog = () => {
  isEditContract.value = false // 新增模式
  contractUploadVisible.value = true
}

/** 处理合同上传成功 */
const handleContractUploadSuccess = async (data: any) => {
  try {
    console.log('合同上传成功:', data)
    console.log('data.contractFileUrl:', data.contractFileUrl)
    console.log('data.contractFile:', data.contractFile)

    // 更新合同信息（ContractUploadDialog内部已经调用了API，这里只需要更新本地状态）
    contractInfo.value = {
      contractName: data.contractName || '个人培训合同',
      contractFile: data.contractFile || '',
      contractFileUrl: data.contractFileUrl || data.contractFile || '',
      contractType: data.contractType || 'paper',
      contractStatus: 'signed', // 确认成功后标记为已签署
      contractNumber: data.contractNumber || '',
      signingDate: data.signDate || data.signingDate || new Date().toISOString().slice(0, 10),
      contractAmount: data.contractAmount || mergedOrderData.value?.orderAmount?.toString() || ''
    }

    console.log('=== 更新后的合同信息 ===')
    console.log('contractInfo.value:', contractInfo.value)
    console.log('接收到的数据:', data)

    // 刷新订单详情以获取最新数据
    await fetchOrderDetail()

    // 获取最新的合同信息
    await fetchContractInfo()
  } catch (error: any) {
    console.error('合同信息更新失败:', error)
    ElMessage.error(`合同信息更新失败：${error?.message || '请重试'}`)
  }
}

/** 获取合同信息 */
const fetchContractInfo = async () => {
  try {
    const orderId = mergedOrderData.value?.id || props.orderData?.id
    if (!orderId) return

    console.log('获取合同信息，订单ID:', orderId)
    const response = await IndividualTrainingOrderApi.getContractInfo(orderId)

    console.log('合同信息获取成功:', response)
    console.log('response类型:', typeof response)
    console.log('response字段:', response ? Object.keys(response) : 'response为空')

    if (response) {
      const contractData = response
      console.log('合同数据详情:', contractData)

      const updatedContractInfo = {
        contractName: contractData.contractName || contractInfo.value.contractName,
        contractFile: contractInfo.value.contractFile, // 接口中没有contractFile字段
        contractFileUrl: contractData.contractFileUrl || contractInfo.value.contractFileUrl,
        contractType: contractData.contractType || contractInfo.value.contractType,
        contractStatus: contractData.contractStatus || contractInfo.value.contractStatus,
        contractNumber: contractData.contractNumber || contractInfo.value.contractNumber,
        signingDate: contractData.signDate || contractInfo.value.signingDate,
        contractAmount: contractData.contractAmount?.toString() || contractInfo.value.contractAmount
      }

      console.log('更新前的合同信息:', contractInfo.value)
      contractInfo.value = updatedContractInfo
      console.log('更新后的合同信息:', contractInfo.value)
    } else {
      console.log('合同信息响应为空')
    }
  } catch (error: any) {
    console.error('获取合同信息失败:', error)
    // 不显示错误提示，因为可能合同信息接口不存在
  }
}

/** 处理收款确认 */
const onPaymentConfirm = async (paymentData: any) => {
  try {
    console.log('收款确认数据:', paymentData)

    // 调用收款确认接口
    const response = await IndividualTrainingOrderApi.confirmPayment({
      orderId: mergedOrderData.value?.id || props.orderData?.id,
      paymentAmount: parseFloat(paymentData.actualAmount),
      paymentType: paymentData.paymentMethod,
      paymentTime: paymentData.paymentDate,
      transactionId: `TXN${Date.now()}`,
      remark: paymentData.paymentNotes
    })

    // 由于响应拦截器已经处理了错误情况，这里直接使用返回的数据
    console.log('收款确认成功:', response)

    // 更新收款信息
    paymentInfo.value = {
      actualAmount: paymentData.actualAmount?.toString() || '',
      paymentMethod: paymentData.paymentMethod || '',
      paymentDate: paymentData.paymentDate || '',
      operator: '当前用户', // TODO: 从用户信息中获取
      paymentNotes: paymentData.paymentNotes || ''
    }

    // 更新订单状态（如果订单状态为待支付，则更新为已支付）
    if (mergedOrderData.value?.orderStatus === 'pending_payment') {
      // 创建更新后的订单数据
      const updatedOrderData = {
        ...mergedOrderData.value,
        orderStatus: 'in_progress',
        paymentStatus: 'paid',
        learningStatus: 'learning'
      }

      // 通知父组件更新订单状态
      emit('order-status-updated', updatedOrderData)
    }

    // 通知父组件收款确认
    emit('payment-confirmed', paymentData)

    // 显示成功提示
    ElMessage.success('收款确认成功！订单状态已更新为"执行中"')

    // 刷新订单详情以获取最新的收款记录
    await fetchOrderDetail()

    // 获取收款记录并回写详情页面
    await fetchPaymentRecords()
  } catch (error: any) {
    console.error('收款确认失败:', error)
    ElMessage.error(`收款确认失败：${error?.message || '请重试'}`)
  }
}

/** 获取收款记录并回写详情页面 */
const fetchPaymentRecords = async () => {
  try {
    const orderId = mergedOrderData.value?.id || props.orderData?.id
    if (!orderId) return

    // 调用收款记录接口
    const response = await IndividualTrainingOrderApi.getPaymentList({
      orderId: orderId,
      page: 1,
      size: 10
    })

    console.log('收款记录获取成功:', response)

    // 更新收款信息
    if (response && response.records && response.records.length > 0) {
      const latestPayment = response.records[0]
      paymentInfo.value = {
        actualAmount: latestPayment.paymentAmount?.toString() || '',
        paymentMethod: latestPayment.paymentType || '',
        paymentDate: latestPayment.paymentTime
          ? new Date(latestPayment.paymentTime).toISOString().slice(0, 19).replace('T', ' ')
          : '',
        operator: latestPayment.operatorName || '系统',
        paymentNotes: latestPayment.remark || ''
      }

      console.log('收款信息已更新:', paymentInfo.value)
    }
  } catch (error: any) {
    console.error('获取收款记录失败:', error)
  }
}

/** 显示审批弹窗 */
const showApprovalDialog = () => {
  approvalVisible.value = true
}

/** 处理审批成功 */
const handleApprovalSuccess = async (data: any) => {
  try {
    console.log('审批成功:', data)

    // OrderApprovalDialog内部已经调用了审批接口，这里只需要刷新数据
    console.log('审批操作已完成，刷新数据')
    ElMessage.success('审批操作成功')

    // 刷新审批列表
    await initApprovalList()
    // 刷新订单详情
    await fetchOrderDetail()
  } catch (error: any) {
    console.error('数据刷新失败:', error)
    ElMessage.error(`数据刷新失败：${error?.message || '请重试'}`)
  }
}

/** 处理审批状态变化 */
const handleApprovalStatusChange = (updatedOrderData: any) => {
  console.log('审批状态已更新:', updatedOrderData)
  // 通知父组件更新订单状态
  emit('order-status-updated', updatedOrderData)
}

/** 查看合同 */
const viewContract = () => {
  const contractUrl = contractInfo.value.contractFile || contractInfo.value.contractFileUrl
  if (contractUrl) {
    window.open(contractUrl, '_blank')
  } else {
    ElMessage.warning('合同文件不存在，无法查看')
  }
}

/** 下载合同 */
const downloadContract = () => {
  const contractUrl = contractInfo.value.contractFile || contractInfo.value.contractFileUrl
  if (contractUrl) {
    const link = document.createElement('a')
    link.href = contractUrl
    link.download = contractInfo.value.contractName || 'contract'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('合同下载成功！')
  } else {
    ElMessage.warning('合同文件不存在，无法下载')
  }
}

/** 编辑合同 */
const editContract = () => {
  console.log('=== 点击编辑合同按钮 ===')
  console.log('当前mergedOrderData:', mergedOrderData.value)
  console.log('当前contractInfo:', mergedOrderData.value?.contractInfo)
  console.log('mergedOrderData.contractFileUrl:', mergedOrderData.value?.contractFileUrl)
  console.log(
    'mergedOrderData.contractInfo?.attachmentPath:',
    mergedOrderData.value?.contractInfo?.attachmentPath
  )

  isEditContract.value = true
  contractUploadVisible.value = true

  console.log('设置isEditContract为true')
}

/** 获取收款方式文本 */
const getPaymentMethodText = (method: string) => {
  const methodMap = {
    bank_transfer: '银行转账',
    alipay: '支付宝',
    wechat_pay: '微信支付',
    cash: '现金',
    check: '支票',
    other: '其他'
  }
  return methodMap[method] || method
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string | number | Date): string => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return String(dateTime)
  }
}

/** 初始化审批列表 */
const initApprovalList = async () => {
  try {
    const orderId = mergedOrderData.value?.id || props.orderData?.id
    if (!orderId) {
      console.log('没有订单ID，跳过审批列表获取')
      return
    }

    // 调用审批列表接口
    const response = await IndividualTrainingOrderApi.getApprovalList({
      orderId: orderId,
      page: 1,
      size: 10
    })

    // 由于响应拦截器已经处理了错误情况，这里直接使用返回的数据
    if (response && response.records) {
      approvalList.value = response.records.map((record: any) => ({
        id: record.approvalId?.toString() || '',
        operatorName: record.approverName || '',
        operatorRole: '审批人',
        action:
          record.approvalResult === 'approved'
            ? '审批通过'
            : record.approvalResult === 'rejected'
              ? '审批驳回'
              : '待审批',
        status: record.approvalResult || 'pending',
        createTime: record.approvalTime || ''
      }))
    } else {
      approvalList.value = []
    }
  } catch (error: any) {
    console.error('获取审批列表失败:', error)
    approvalList.value = []
  }
}

/** 初始化最近操作日志 */
const initRecentLogs = async () => {
  try {
    const orderNo = props.orderNo || mergedOrderData.value?.orderNo || props.orderData?.orderNo
    if (!orderNo) {
      console.log('没有订单号，跳过操作日志获取')
      return
    }

    console.log('开始获取操作日志，订单号:', orderNo)

    // 调用操作日志接口，获取最近的3条日志
    const response = await IndividualTrainingOrderApi.getOptLogList({
      orderNo: orderNo,
      page: 1,
      size: 3
    })

    console.log('操作日志接口返回:', response)

    // 处理不同的响应数据结构
    let logs: any[] = []
    if (response && response.records) {
      // 标准结构：response.records
      logs = response.records
    } else if ((response as any).data && (response as any).data.list) {
      // 嵌套结构：response.data.list
      logs = (response as any).data.list
    } else if ((response as any).list) {
      // 直接结构：response.list
      logs = (response as any).list
    }

    if (logs && logs.length > 0) {
      recentLogs.value = logs.slice(0, 3)
      console.log('设置最近操作日志:', recentLogs.value)
    } else {
      recentLogs.value = []
      console.log('没有找到操作日志记录')
    }
  } catch (error: any) {
    console.error('获取操作日志失败:', error)
    recentLogs.value = []
  }
}

/** 获取线索的订单来源 */
const fetchLeadSource = async (leadId: string | number) => {
  try {
    console.log('开始获取线索信息，线索ID:', leadId)

    // 调用线索详情接口获取订单来源
    // 这里需要根据实际的线索接口来调用
    // 暂时使用模拟数据，实际应该调用真实的线索接口
    const leadSource = await getLeadSourceFromAPI(leadId)

    if (leadSource) {
      // 更新订单数据中的订单来源
      if (apiOrderData.value) {
        apiOrderData.value.orderSource = leadSource
      }
      console.log('从线索获取订单来源成功:', leadSource)
    }
  } catch (error: any) {
    console.error('获取线索订单来源失败:', error)
  }
}

/** 从线索接口获取订单来源（模拟实现） */
const getLeadSourceFromAPI = async (leadId: string | number): Promise<string | null> => {
  try {
    // 这里应该调用真实的线索接口
    // 暂时返回模拟数据，实际实现时需要替换
    console.log('调用线索接口获取订单来源，线索ID:', leadId)

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 模拟返回数据，实际应该根据线索ID查询
    const mockLeadSources: Record<string, string> = {
      '2015': '线上小程序',
      '2016': '线下报名',
      '2017': '电话咨询',
      '2018': '朋友推荐'
    }

    return mockLeadSources[leadId.toString()] || null
  } catch (error) {
    console.error('线索接口调用失败:', error)
    return null
  }
}

/** 获取审批状态样式类 */
const getApprovalStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'pending',
    approved: 'approved',
    rejected: 'rejected'
  }
  return statusMap[status] || 'pending'
}

/** 获取审批状态类型 */
const getApprovalStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 解析日志内容 */
const parseLogContent = (logContent: string) => {
  try {
    if (!logContent) return null
    return JSON.parse(logContent)
  } catch (error) {
    console.error('解析日志内容失败:', error)
    return null
  }
}

/** 格式化字段名称 */
const formatFieldName = (field: string): string => {
  const fieldMap: Record<string, string> = {
    projectName: '项目名称',
    totalAmount: '总金额',
    university: '高校',
    enterprise: '企业'
  }
  return fieldMap[field] || field
}

/** 获取审批状态文本 */
const getApprovalStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已驳回'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  initApprovalList()
})

// 监听visible变化，当显示时调用接口
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderNo) {
      // 当抽屉显示且有订单号时，调用接口获取数据
      fetchOrderDetail()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .course-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;

    .course-title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .info-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #666;
        font-size: 14px;
        min-width: 100px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        flex: 1;

        &.amount {
          color: #67c23a;
          font-weight: 600;
        }
      }
    }
  }

  .detail-module {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .module-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      .module-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }

    .module-content {
      padding: 20px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 100px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.amount {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }

      .contract-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .contract-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .contract-actions {
          display: flex;
          gap: 8px;
        }

        .file-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 4px;
          margin-top: 8px;

          .file-name {
            flex: 1;
            color: #333;
            font-size: 14px;
          }
        }

        .upload-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
        }

        .manage-actions {
          display: flex;
          gap: 8px;
        }
      }

      .no-contract {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #999;
        padding: 20px;
        text-align: center;
        justify-content: center;
      }

      .approval-timeline {
        .timeline-item {
          display: flex;
          margin-bottom: 16px;

          .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            margin-top: 6px;

            &.pending {
              background: #e6a23c;
            }

            &.approved {
              background: #67c23a;
            }

            &.rejected {
              background: #f56c6c;
            }
          }

          .timeline-content {
            flex: 1;

            .timeline-time {
              font-size: 12px;
              color: #999;
              margin-bottom: 4px;
            }

            .timeline-action {
              font-size: 14px;
              color: #333;
              margin-bottom: 4px;
            }
          }
        }
      }

      .no-approval {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #999;
        padding: 20px;
        text-align: center;
        justify-content: center;
      }

      .log-list {
        .log-item {
          display: flex;
          margin-bottom: 16px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 6px;
          border-left: 3px solid #409eff;

          .log-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #409eff;
            margin-right: 12px;
            margin-top: 8px;
            flex-shrink: 0;
          }

          .log-content {
            flex: 1;

            .log-time {
              font-size: 12px;
              color: #999;
              margin-bottom: 6px;
            }

            .log-message {
              font-size: 14px;
              color: #333;
              margin-bottom: 6px;
              display: flex;
              align-items: center;
              gap: 8px;

              .operator-name {
                font-weight: 600;
                color: #409eff;
              }

              .log-action {
                color: #666;
              }
            }

            .log-details {
              font-size: 13px;
              color: #666;
              background: #fff;
              padding: 8px 12px;
              border-radius: 4px;
              border: 1px solid #e4e7ed;
              line-height: 1.4;

              .log-creation-content {
                .parsed-content {
                  .log-action-text {
                    font-weight: 600;
                    color: #409eff;
                    margin-bottom: 8px;
                    font-size: 14px;
                  }

                  .log-details-grid {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    margin-bottom: 8px;

                    .detail-item {
                      background: #f5f7fa;
                      padding: 4px 8px;
                      border-radius: 4px;
                      font-size: 12px;

                      .detail-label {
                        color: #666;
                        margin-right: 4px;
                      }

                      .detail-value {
                        color: #333;
                        font-weight: 500;
                      }
                    }
                  }

                  .log-description {
                    color: #666;
                    font-style: italic;
                    background: #f8f9fa;
                    padding: 8px;
                    border-radius: 4px;
                    border-left: 3px solid #409eff;
                  }
                }

                .log-content-text {
                  color: #666;
                  line-height: 1.5;
                }
              }
            }
          }
        }

        .no-logs {
          padding: 40px 20px;
          text-align: center;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.view-log-btn {
  margin-left: auto;
}
</style>
