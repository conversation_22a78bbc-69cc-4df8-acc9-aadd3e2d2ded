<!--
  页面名称：个人培训订单发起审批弹窗
  功能描述：提交个人培训订单审批申请
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="发起审批"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="submit-approval-container">
      <!-- 订单信息展示 -->
      <div class="order-info">
        <h4>订单信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订单号：</span>
            <span class="value">{{ orderData?.orderNumber || orderData?.orderNo || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">学员姓名：</span>
            <span class="value">{{ orderData?.studentName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">课程名称：</span>
            <span class="value">{{ orderData?.courseName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ formatAmount(orderData?.orderAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- 审批信息表单 -->
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="审批类型*" prop="approvalType">
          <el-select v-model="form.approvalType" placeholder="请选择审批类型" style="width: 100%">
            <el-option label="培训审批" value="training_approval" />
            <el-option label="合同审批" value="contract_approval" />
            <el-option label="费用审批" value="cost_approval" />
            <el-option label="其他审批" value="other_approval" />
          </el-select>
        </el-form-item>

        <el-form-item label="审批级别*" prop="approvalLevel">
          <el-input-number
            v-model="form.approvalLevel"
            :min="1"
            :max="5"
            style="width: 100%"
            placeholder="请输入审批级别"
          />
        </el-form-item>

        <el-form-item label="审批人ID*" prop="approverIds">
          <el-select
            v-model="form.approverIds"
            multiple
            placeholder="请选择审批人"
            style="width: 100%"
          >
            <el-option label="张经理 (1001)" :value="1001" />
            <el-option label="李总监 (1002)" :value="1002" />
            <el-option label="王总 (1003)" :value="1003" />
            <el-option label="赵副总 (1004)" :value="1004" />
          </el-select>
        </el-form-item>

        <el-form-item label="下一级审批人">
          <el-select
            v-model="form.nextApproverIds"
            multiple
            placeholder="请选择下一级审批人 (可选)"
            style="width: 100%"
          >
            <el-option label="张经理 (1001)" :value="1001" />
            <el-option label="李总监 (1002)" :value="1002" />
            <el-option label="王总 (1003)" :value="1003" />
            <el-option label="赵副总 (1004)" :value="1004" />
          </el-select>
        </el-form-item>

        <el-form-item label="审批意见">
          <el-input
            v-model="form.approvalOpinion"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见 (可选)"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息 (可选)"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="操作人姓名*" prop="operatorName">
          <el-input
            v-model="form.operatorName"
            placeholder="请输入操作人姓名"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 发起审批 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  approvalType: 'training_approval',
  approvalLevel: 1,
  approverIds: [],
  nextApproverIds: [],
  approvalOpinion: '',
  remark: '',
  operatorName: '当前用户' // 临时默认值
})

// 表单验证规则
const rules: FormRules = {
  approvalType: [{ required: true, message: '请选择审批类型', trigger: 'change' }],
  approvalLevel: [{ required: true, message: '请输入审批级别', trigger: 'blur' }],
  approverIds: [{ required: true, message: '请选择审批人', trigger: 'change' }],
  operatorName: [{ required: true, message: '请输入操作人姓名', trigger: 'blur' }]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const formatAmount = (amount: number | string) => {
  if (!amount) return '0.00'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  form.approvalType = 'training_approval'
  form.approvalLevel = 1
  form.approverIds = []
  form.nextApproverIds = []
  form.approvalOpinion = ''
  form.remark = ''
  form.operatorName = '当前用户'
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 调用发起审批API
    const approvalData = {
      orderId: props.orderData?.id || props.orderData?.orderId,
      approvalType: form.approvalType,
      approvalLevel: form.approvalLevel,
      approverIds: form.approverIds,
      approvalOpinion: form.approvalOpinion || undefined,
      nextApproverIds: form.nextApproverIds.length > 0 ? form.nextApproverIds : undefined,
      remark: form.remark || undefined,
      operatorName: form.operatorName
    }

    console.log('发起审批数据:', approvalData)

    try {
      const { IndividualTrainingOrderApi } = await import(
        '@/api/OrderCenter/IndividualtrainingOrder'
      )

      await IndividualTrainingOrderApi.submitApproval(approvalData)

      ElMessage.success('审批申请提交成功')
      emit('success', { ...approvalData, orderData: props.orderData })
      visible.value = false
      resetForm()
    } catch (apiError) {
      console.error('发起审批API调用失败:', apiError)
      ElMessage.error('审批申请提交失败，请重试')
    }
  } catch (error) {
    console.error('发起审批失败:', error)
    ElMessage.error('发起审批失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态，初始化表单数据
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      console.log('=== SubmitApprovalDialog: 初始化表单数据 ===')
      console.log('订单数据:', props.orderData)

      // 重置表单
      resetForm()

      // 设置默认值
      form.operatorName = '当前用户'
    }
  }
)
</script>

<style scoped lang="scss">
.submit-approval-container {
  .order-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.amount {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>



