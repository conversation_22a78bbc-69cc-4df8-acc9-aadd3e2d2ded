<!--
  页面名称：个人培训订单功能测试页面
  功能描述：测试订单详情、审批、合同上传、收款等功能
-->
<template>
  <div class="test-page">
    <div class="page-header">
      <h2>个人培训订单功能测试</h2>
      <p>测试订单来源绑定、审批弹窗、合同上传、审批列表等功能</p>
    </div>

    <div class="test-section">
      <h3>1. 订单来源绑定测试</h3>
      <div class="test-content">
        <el-button @click="testOrderSourceBinding">测试订单来源绑定</el-button>
        <div class="test-result">
          <p>订单来源值: {{ testOrderSource }}</p>
          <p>订单来源文本: {{ testOrderSourceText }}</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 订单详情弹窗测试</h3>
      <div class="test-content">
        <el-button type="primary" @click="showOrderDetail">查看订单详情</el-button>
        <p>点击按钮查看订单详情弹窗，测试审批、合同上传、收款等功能</p>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 审批功能测试</h3>
      <div class="test-content">
        <el-button type="success" @click="testApprovalDialog">测试审批弹窗</el-button>
        <p>测试审批弹窗的显示和功能</p>
      </div>
    </div>

    <div class="test-section">
      <h3>4. 合同上传测试</h3>
      <div class="test-content">
        <el-button type="warning" @click="testContractUpload">测试合同上传</el-button>
        <p>测试合同上传弹窗的显示和功能</p>
      </div>
    </div>

    <div class="test-section">
      <h3>5. 审批列表测试</h3>
      <div class="test-content">
        <el-button type="info" @click="testApprovalList">测试审批列表</el-button>
        <p>测试审批列表组件的显示和功能</p>
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <IndividualtrainingOrderView
      v-model:visible="orderDetailVisible"
      :order-data="testOrderData"
      @edit="handleEdit"
      @payment-confirmed="handlePaymentConfirmed"
      @order-status-updated="handleOrderStatusUpdated"
    />

    <!-- 审批弹窗 -->
    <OrderApprovalDialog
      v-model:visible="approvalVisible"
      :order-data="testOrderData"
      @success="handleApprovalSuccess"
    />

    <!-- 合同上传弹窗 -->
    <ContractUploadDialog
      v-model:visible="contractUploadVisible"
      :order-data="testOrderData"
      @success="handleContractUploadSuccess"
    />

    <!-- 审批列表弹窗 -->
    <el-dialog v-model="approvalListVisible" title="审批列表测试" width="800px">
      <ApprovalList :order-id="testOrderData.id || 0" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import IndividualtrainingOrderView from './components/IndividualtrainingOrderView.vue'
import OrderApprovalDialog from './components/OrderApprovalDialog.vue'
import ContractUploadDialog from './components/ContractUploadDialog.vue'
import ApprovalList from './components/ApprovalList.vue'
import {
  getOrderSourceText,
  ORDER_SOURCE,
  IndividualTrainingOrderApi,
  type IndividualTrainingOrder,
  type ContractUploadParams,
  type ApproveParams
} from '@/api/OrderCenter/IndividualtrainingOrder'

// 测试数据 - 使用真实的订单数据结构
const testOrderData = ref<IndividualTrainingOrder>({
  id: 1,
  orderNo: 'PT202406001',
  orderType: 'training',
  businessLine: '项目管理',
  businessOpportunity: 'OPP202406009',
  associatedLead: 'LEAD202406009',
  studentName: '张三',
  studentPhone: '***********',
  studentEmail: '<EMAIL>',
  courseName: '项目管理PMP认证课程',
  courseType: 'certification',
  orderSource: ORDER_SOURCE.ONLINE_MINIPROGRAM,
  orderAmount: 4500,
  orderStatus: 'pending_approval',
  paymentStatus: 'pending',
  learningStatus: 'not_started',
  examStatus: 'not_registered',
  registrationTime: '2024-06-10',
  remark: '测试订单',
  createTime: '2024-06-10 09:00:00',
  updateTime: '2024-06-10 09:00:00'
})

// 弹窗显示状态
const orderDetailVisible = ref(false)
const approvalVisible = ref(false)
const contractUploadVisible = ref(false)
const approvalListVisible = ref(false)

// 测试结果
const testOrderSource = ref('')
const testOrderSourceText = ref('')

// 测试方法
const testOrderSourceBinding = async () => {
  try {
    testOrderSource.value = testOrderData.value.orderSource
    testOrderSourceText.value = await getOrderSourceText(testOrderData.value.orderSource)
    ElMessage.success('订单来源绑定测试成功')
  } catch (error) {
    console.error('订单来源绑定测试失败:', error)
    ElMessage.error('订单来源绑定测试失败')
  }
}

// 获取真实订单数据
const fetchRealOrderData = async () => {
  try {
    // 这里可以调用真实的API获取订单数据
    // const result = await IndividualTrainingOrderApi.getOrderByOrderNo('PT202406001')
    // testOrderData.value = result
    ElMessage.info('使用模拟数据，实际项目中可调用真实API')
  } catch (error) {
    console.error('获取订单数据失败:', error)
    ElMessage.error('获取订单数据失败')
  }
}

const showOrderDetail = () => {
  orderDetailVisible.value = true
}

const testApprovalDialog = () => {
  approvalVisible.value = true
}

const testContractUpload = () => {
  contractUploadVisible.value = true
}

const testApprovalList = () => {
  approvalListVisible.value = true
}

// 事件处理
const handleEdit = async (data: any) => {
  console.log('编辑订单:', data)
  try {
    // 这里可以调用真实的更新API
    // await IndividualTrainingOrderApi.updateOrder({
    //   id: data.id,
    //   studentName: data.studentName,
    //   courseName: data.courseName,
    //   orderAmount: data.orderAmount,
    //   remark: data.remark
    // })
    ElMessage.success('编辑功能测试成功（模拟）')
  } catch (error) {
    console.error('编辑订单失败:', error)
    ElMessage.error('编辑订单失败')
  }
}

const handlePaymentConfirmed = async (data: any) => {
  console.log('收款确认:', data)
  try {
    // 这里可以调用真实的收款确认API
    // await IndividualTrainingOrderApi.confirmPayment({
    //   orderId: data.orderId,
    //   actualAmount: data.actualAmount,
    //   paymentMethod: data.paymentMethod,
    //   paymentDate: data.paymentDate,
    //   operator: data.operator,
    //   remark: data.remark
    // })
    ElMessage.success('收款确认功能测试成功（模拟）')
  } catch (error) {
    console.error('收款确认失败:', error)
    ElMessage.error('收款确认失败')
  }
}

const handleOrderStatusUpdated = async (data: any) => {
  console.log('订单状态更新:', data)
  try {
    // 这里可以调用真实的订单状态更新API
    // await IndividualTrainingOrderApi.updateOrder({
    //   id: data.id,
    //   orderStatus: data.orderStatus,
    //   paymentStatus: data.paymentStatus,
    //   learningStatus: data.learningStatus
    // })
    ElMessage.success('订单状态更新功能测试成功（模拟）')
  } catch (error) {
    console.error('订单状态更新失败:', error)
    ElMessage.error('订单状态更新失败')
  }
}

const handleApprovalSuccess = async (data: any) => {
  console.log('审批成功:', data)
  try {
    // 这里可以调用真实的审批API
    // await IndividualTrainingOrderApi.approve({
    //   approvalId: data.approvalId,
    //   orderId: data.orderId,
    //   approvalResult: data.approvalResult,
    //   approvalOpinion: data.approvalOpinion
    // })
    ElMessage.success('审批功能测试成功（模拟）')
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败')
  }
}

const handleContractUploadSuccess = async (data: any) => {
  console.log('合同上传成功:', data)
  try {
    // 这里可以调用真实的合同上传API
    // const contractParams: ContractUploadParams = {
    //   orderId: testOrderData.value.id!,
    //   tenantId: 1, // 从用户信息或store中获取
    //   contractFile: data.contractFile || '',
    //   contractName: data.contractName,
    //   contractNumber: data.contractNumber,
    //   startDate: data.signDate,
    //   endDate: data.signDate,
    //   amount: Number(data.contractAmount),
    //   signer: '当前用户' // 从用户信息中获取
    // }
    // const result = await IndividualTrainingOrderApi.uploadContract(contractParams)
    ElMessage.success('合同上传功能测试成功（模拟）')
  } catch (error) {
    console.error('合同上传失败:', error)
    ElMessage.error('合同上传失败')
  }
}

// 生命周期
onMounted(() => {
  // 页面加载时初始化数据
  fetchRealOrderData()
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.test-content p {
  color: #666;
  margin: 0;
}

.test-result {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.test-result p {
  margin: 5px 0;
  font-family: monospace;
}
</style>
