<!--
  页面名称：阿姨详情
  功能描述：查看阿姨详细信息，包含基本信息和评级信息
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="`阿姨详情: ${practitionerDetail?.name || ''}`"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
  >
    <div class="practitioner-detail" v-loading="loading">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <h3 class="section-title">基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">姓名:</span>
                <span class="value">{{ practitionerDetail?.name || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">年龄:</span>
                <span class="value">{{ practitionerDetail?.age || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">籍贯:</span>
                <span class="value">{{ practitionerDetail?.hometown || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机:</span>
                <span class="value">{{ formatPhone(practitionerDetail?.phone) || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">身份证:</span>
                <span class="value">
                  {{ formatIdCard(practitionerDetail?.idCard) || '-' }}
                </span>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h3 class="section-title">服务信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">主要服务类型:</span>
                <span class="value">{{ practitionerDetail?.serviceType || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">从业年限:</span>
                <span class="value">{{
                  practitionerDetail?.experienceYears
                    ? `${practitionerDetail.experienceYears}年`
                    : '-'
                }}</span>
              </div>
              <div class="info-item">
                <span class="label">综合评级:</span>
                <span class="value rating-value">
                  {{ practitionerDetail?.rating || '-' }}
                  <span v-if="practitionerDetail?.rating" class="stars">
                    <i
                      v-for="i in 5"
                      :key="i"
                      class="fas fa-star"
                      :class="{
                        filled: i <= Math.floor(practitionerDetail.rating),
                        half:
                          i === Math.ceil(practitionerDetail.rating) &&
                          practitionerDetail.rating % 1 !== 0
                      }"
                    ></i>
                  </span>
                </span>
              </div>
              <div class="info-item">
                <span class="label">累计单数:</span>
                <span class="value">{{ practitionerDetail?.totalOrders || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">累计收入:</span>
                <span class="value">{{ formatMoney(practitionerDetail?.totalIncome) || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">客户满意度:</span>
                <span class="value">{{ practitionerDetail?.customerSatisfaction || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">平台状态:</span>
                <span class="value">
                  <el-tag :type="getStatusTag(practitionerDetail?.platformStatus)" size="small">
                    {{ getStatusText(practitionerDetail?.platformStatus) }}
                  </el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">当前状态:</span>
                <span class="value">{{ practitionerDetail?.currentStatus || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 资质文件模块 -->
          <div class="info-section">
            <h3 class="section-title">资质文件</h3>
            <div class="qualification-files">
              <!-- 身份证正反面 -->
              <div class="file-category">
                <div class="category-header">
                  <i class="fas fa-id-card category-icon"></i>
                  <span class="category-title">身份证正反面</span>
                </div>
                <div class="file-list">
                  <div class="file-item" v-for="file in idCardFiles" :key="file.id">
                    <div class="file-info">
                      <img :src="getFileIcon(file.fileExtension)" class="file-icon" />
                      <span class="file-name">{{ file.fileName }}</span>
                    </div>
                    <div class="file-actions">
                      <el-button size="small" type="primary" @click="viewFile(file)"
                        >查看</el-button
                      >
                    </div>
                  </div>
                  <div v-if="idCardFiles.length === 0" class="no-files"> 暂无身份证文件 </div>
                </div>
              </div>

              <!-- 健康证 -->
              <div class="file-category">
                <div class="category-header">
                  <i class="fas fa-heartbeat category-icon"></i>
                  <span class="category-title">健康证</span>
                </div>
                <div class="file-list">
                  <div class="file-item" v-for="file in healthCertFiles" :key="file.id">
                    <div class="file-info">
                      <img :src="getFileIcon(file.fileExtension)" class="file-icon" />
                      <span class="file-name">{{ file.fileName }}</span>
                    </div>
                    <div class="file-actions">
                      <el-button size="small" type="primary" @click="viewFile(file)"
                        >查看</el-button
                      >
                    </div>
                  </div>
                  <div v-if="healthCertFiles.length === 0" class="no-files"> 暂无健康证文件 </div>
                </div>
              </div>

              <!-- 专业技能证书 -->
              <div class="file-category">
                <div class="category-header">
                  <i class="fas fa-certificate category-icon"></i>
                  <span class="category-title">专业技能证书</span>
                </div>
                <div class="file-list">
                  <div class="file-item" v-for="file in skillCertFiles" :key="file.id">
                    <div class="file-info">
                      <img :src="getFileIcon(file.fileExtension)" class="file-icon" />
                      <span class="file-name">{{ file.fileName }}</span>
                    </div>
                    <div class="file-actions">
                      <el-button size="small" type="primary" @click="viewFile(file)"
                        >查看</el-button
                      >
                    </div>
                  </div>
                  <div v-if="skillCertFiles.length === 0" class="no-files"> 暂无技能证书文件 </div>
                </div>
              </div>

              <!-- 其他附件 -->
              <div class="file-category">
                <div class="category-header">
                  <i class="fas fa-paperclip category-icon"></i>
                  <span class="category-title">其他附件</span>
                </div>
                <div class="file-list">
                  <div class="file-item" v-for="file in otherFiles" :key="file.id">
                    <div class="file-info">
                      <img :src="getFileIcon(file.fileExtension)" class="file-icon" />
                      <span class="file-name">{{ file.fileName }}</span>
                    </div>
                    <div class="file-actions">
                      <el-button size="small" type="primary" @click="viewFile(file)"
                        >查看</el-button
                      >
                    </div>
                  </div>
                  <div v-if="otherFiles.length === 0" class="no-files"> 暂无其他附件文件 </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="服务记录" name="service">
          <div class="service-records">
            <!-- 日历头部 -->
            <div class="calendar-header">
              <button class="nav-btn" @click="previousMonth">
                <i class="fas fa-chevron-left"></i>
              </button>
              <h3 class="month-title">{{ currentYear }}年{{ currentMonth }}月</h3>
              <button class="nav-btn" @click="nextMonth">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>

            <!-- 星期标题 -->
            <div class="weekdays">
              <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
            </div>

            <!-- 日历网格 -->
            <div class="calendar-grid">
              <div
                v-for="date in calendarDates"
                :key="date.key"
                class="calendar-day"
                :class="{
                  'other-month': !date.isCurrentMonth,
                  'has-service': date.hasService,
                  today: date.isToday
                }"
              >
                <div v-if="date.isCurrentMonth" class="date-number">{{ date.day }}</div>
                                 <div v-if="date.isCurrentMonth" class="service-status">
                   <button
                     v-if="date.taskCount > 3"
                     class="service-btn busy-btn"
                     @click="viewServiceRecord(date.serviceRecord)"
                   >
                     忙碌
                   </button>
                   <button v-else class="service-btn available-btn">
                     空闲
                   </button>
                 </div>
              </div>
            </div>
          </div>
        </el-tab-pane>


      </el-tabs>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getPractitionerDetail, getPractitionerTaskStats, type PractitionerTaskStats } from '@/api/mall/employment/practitioner'

const props = defineProps<{
  visible: boolean
  practitioner: any
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 当前激活的标签页
const activeTab = ref('basic')

// 加载状态
const loading = ref(false)

// 阿姨详情数据
const practitionerDetail = ref<any>(null)

// 文件分类
const idCardFiles = ref<any[]>([])
const healthCertFiles = ref<any[]>([])
const skillCertFiles = ref<any[]>([])
const otherFiles = ref<any[]>([])

// 服务记录
const serviceRecords = ref<any[]>([])



// 任务统计数据
const taskStats = ref<PractitionerTaskStats[]>([])

// 日历相关数据
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth() + 1)
const weekdays = ['日', '一', '二', '三', '四', '五', '六']
const calendarDates = ref<any[]>([])

// 监听practitioner变化，加载详情数据
watch(
  () => props.practitioner,
  async (newPractitioner) => {
    console.log('[PractitionerDetail] practitioner变化:', newPractitioner)
    if (newPractitioner && props.visible) {
      console.log('[PractitionerDetail] 开始加载详情，ID:', newPractitioner.id)
      await loadPractitionerDetail(newPractitioner.id)
    }
  },
  { immediate: true }
)

// 监听visible变化
watch(
  () => props.visible,
  async (visible) => {
    console.log('[PractitionerDetail] visible变化:', visible)
    if (visible && props.practitioner) {
      console.log('[PractitionerDetail] 开始加载详情，ID:', props.practitioner.id)
      await loadPractitionerDetail(props.practitioner.id)
    }
  }
)

// 加载阿姨详情
const loadPractitionerDetail = async (id: number) => {
  try {
    loading.value = true
    const res = await getPractitionerDetail(id)
    console.log('[PractitionerDetail] API响应:', res)

    // 处理不同的响应格式
    let detailData = null
    if (res && res.data) {
      // 如果响应包含data字段
      detailData = res.data
    } else if (res && res.id) {
      // 如果响应直接是详情对象
      detailData = res
    }

    if (detailData) {
      practitionerDetail.value = detailData
      console.log('[PractitionerDetail] 处理后的详情数据:', detailData)

      // 分类资质文件
      classifyQualifications(detailData.qualifications || [])

      // 设置服务记录
      serviceRecords.value = detailData.recentServiceRecords || []



      // 加载任务统计数据
      await loadTaskStats(detailData.auntOneid)

      // 生成日历数据
      generateCalendarDates()
    } else {
      console.error('[PractitionerDetail] 无法解析详情数据')
      ElMessage.error('获取阿姨详情失败')
    }
  } catch (error) {
    console.error('获取阿姨详情失败:', error)
    ElMessage.error('获取阿姨详情失败')
  } finally {
    loading.value = false
  }
}

// 分类资质文件
const classifyQualifications = (qualifications: any[]) => {
  console.log('[PractitionerDetail] 开始分类资质文件:', qualifications)

  idCardFiles.value = []
  healthCertFiles.value = []
  skillCertFiles.value = []
  otherFiles.value = []

  qualifications.forEach((qual) => {
    console.log('[PractitionerDetail] 处理文件:', qual.fileType, qual.fileName)
    switch (qual.fileType) {
      case 'id_card':
        idCardFiles.value.push(qual)
        break
      case 'health_cert':
        healthCertFiles.value.push(qual)
        break
      case 'skill_cert':
        skillCertFiles.value.push(qual)
        break
      case 'other':
        otherFiles.value.push(qual)
        break
      default:
        console.warn('[PractitionerDetail] 未知文件类型:', qual.fileType)
        otherFiles.value.push(qual)
        break
    }
  })

  console.log('[PractitionerDetail] 分类结果:', {
    idCardFiles: idCardFiles.value.length,
    healthCertFiles: healthCertFiles.value.length,
    skillCertFiles: skillCertFiles.value.length,
    otherFiles: otherFiles.value.length
  })
}

// 加载任务统计数据
const loadTaskStats = async (practitionerId: number | string) => {
  try {
    console.log('[PractitionerDetail] 开始加载任务统计数据，ID:', practitionerId)
    
    // 构建月份参数（格式：yyyy-MM）
    const month = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}`
    
    const res = await getPractitionerTaskStats(String(practitionerId), month)
    console.log('[PractitionerDetail] 任务统计API响应:', res)

    // 处理不同的响应格式
    let statsData = null
    if (res && res.data) {
      // 如果响应包含data字段
      statsData = res.data
    } else if (Array.isArray(res)) {
      // 如果响应直接是数组
      statsData = res
    }

    if (statsData && Array.isArray(statsData)) {
      taskStats.value = statsData
      console.log('[PractitionerDetail] 处理后的任务统计数据:', statsData)
    } else {
      console.warn('[PractitionerDetail] 任务统计数据格式异常:', statsData)
      taskStats.value = []
    }
  } catch (error) {
    console.error('[PractitionerDetail] 获取任务统计数据失败:', error)
    ElMessage.error('获取任务统计数据失败')
    taskStats.value = []
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 格式化手机号
const formatPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化身份证号
const formatIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

// 格式化金额
const formatMoney = (amount: number) => {
  if (!amount) return '-'
  return `¥${amount.toFixed(2)}`
}

// 格式化日期
const formatDate = (dateStr: string | number) => {
  if (!dateStr) return '-'

  try {
    // 如果是时间戳，转换为Date对象
    const date = typeof dateStr === 'number' ? new Date(dateStr) : new Date(dateStr)
    return date.toLocaleDateString()
  } catch (error) {
    console.error('[PractitionerDetail] 日期格式化失败:', dateStr, error)
    return '-'
  }
}

// 获取文件图标
const getFileIcon = (extension: string) => {
  const iconMap: Record<string, string> = {
    jpg: '/icons/image.png',
    jpeg: '/icons/image.png',
    png: '/icons/image.png',
    pdf: '/icons/pdf.png',
    doc: '/icons/word.png',
    docx: '/icons/word.png'
  }
  return iconMap[extension?.toLowerCase()] || '/icons/file.png'
}

// 获取状态标签类型
const getStatusTag = (status: string) => {
  switch (status) {
    case 'cooperating':
      return 'success'
    case 'terminated':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    cooperating: '合作中',
    terminated: '已解约',
    pending: '待审核'
  }
  return statusMap[status] || status
}

// 获取订单状态标签类型
const getOrderStatusTag = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'in_progress':
      return 'warning'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待确认',
    confirmed: '已确认',
    in_progress: '服务中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}



// 查看文件
const viewFile = (file: any) => {
  if (file.fileUrl) {
    window.open(file.fileUrl, '_blank')
  }
}

// 生成日历数据
const generateCalendarDates = () => {
  const dates = []
  const firstDay = new Date(currentYear.value, currentMonth.value - 1, 1)
  const lastDay = new Date(currentYear.value, currentMonth.value, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const today = new Date()
  const isToday = (date: Date) => {
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    )
  }

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    
    // 查找任务统计数据
    const taskStat = taskStats.value.find((stat) => {
      return stat.date === dateKey
    })
    
    // 查找服务记录
    const serviceRecord = serviceRecords.value.find((record) => {
      const serviceDate = new Date(record.serviceStartTime)
      return serviceDate.toDateString() === date.toDateString()
    })

    dates.push({
      key: dateKey,
      date: date,
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === currentMonth.value - 1,
      isToday: isToday(date),
      hasService: !!serviceRecord || (taskStat && taskStat.taskCount > 0),
      serviceRecord: serviceRecord,
      taskCount: taskStat ? taskStat.taskCount : 0
    })
  }

  calendarDates.value = dates
}

// 上个月
const previousMonth = async () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12
    currentYear.value--
  } else {
    currentMonth.value--
  }
  
  // 重新加载任务统计数据
  if (practitionerDetail.value) {
    await loadTaskStats(practitionerDetail.value.auntOneid)
  }
  
  generateCalendarDates()
}

// 下个月
const nextMonth = async () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1
    currentYear.value++
  } else {
    currentMonth.value++
  }
  
  // 重新加载任务统计数据
  if (practitionerDetail.value) {
    await loadTaskStats(practitionerDetail.value.auntOneid)
  }
  
  generateCalendarDates()
}

// 查看服务记录详情
const viewServiceRecord = (record: any) => {
  ElMessage.info(`查看订单: ${record.orderId}`)
  // TODO: 可以打开一个对话框显示详细信息
}

// 预约服务
const bookService = (date: Date) => {
  ElMessage.info(`预约服务: ${date.toLocaleDateString()}`)
  // TODO: 实现预约功能
}
</script>

<style scoped lang="scss">
.practitioner-detail {
  .detail-tabs {
    height: 100%;
  }

  .info-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #666;
          min-width: 100px;
        }

        .value {
          color: #333;
          flex: 1;

          .rating-value {
            display: flex;
            align-items: center;
            gap: 8px;

            .stars {
              .fas {
                color: #ddd;
                font-size: 12px;

                &.filled {
                  color: #f39c12;
                }

                &.half {
                  color: #f39c12;
                }
              }
            }
          }
        }
      }
    }
  }

  .qualification-files {
    .file-category {
      margin-bottom: 25px;

      .category-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;

        .category-icon {
          margin-right: 8px;
          color: #409eff;
        }

        .category-title {
          font-weight: 500;
          color: #333;
        }
      }

      .file-list {
        .file-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin-bottom: 8px;
          background: white;

          .file-info {
            display: flex;
            align-items: center;
            flex: 1;

            .file-icon {
              width: 20px;
              height: 20px;
              margin-right: 10px;
            }

            .file-name {
              color: #333;
              font-size: 14px;
            }
          }

          .file-actions {
            margin-left: 15px;
          }
        }

        .no-files {
          text-align: center;
          color: #999;
          padding: 20px;
          font-style: italic;
        }
      }
    }
  }

  .service-records {
    .calendar-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      padding: 0 10px;

      .nav-btn {
        background: none;
        border: none;
        font-size: 16px;
        color: #666;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background: #f0f0f0;
          color: #333;
        }
      }

      .month-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }

    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      margin-bottom: 10px;

      .weekday {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        font-weight: 500;
        color: #666;
        font-size: 14px;
      }
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      border: 1px solid #e0e0e0;
      background: #f5f5f5;

      .calendar-day {
        min-height: 90px;
        background: white;
        border: 1px solid #e0e0e0;
        padding: 12px 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;

        &.other-month {
          background: #f8f9fa;
          color: #ccc;
        }

        &.today {
          background: #e3f2fd;
          border-color: #2196f3;

          .date-number {
            color: #2196f3;
            font-weight: bold;
          }
        }

                 &.has-service {
           // 移除边框颜色
         }

        .date-number {
          font-size: 18px;
          font-weight: 700;
          color: #000;
          margin-bottom: 8px;
          text-align: center;
        }

        .service-status {
          .service-btn {
            width: 100%;
            max-width: 60px;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: default;
            transition: all 0.2s;
            text-align: center;

            &.available-btn {
              background: rgba(46, 204, 113, 0.1);
              color: #2ecc71;
              border: 1px solid rgba(46, 204, 113, 0.3);

              &:hover {
                background: rgba(46, 204, 113, 0.1);
              }
            }

                         &.busy-btn {
               background: #fff3e0;
               color: #f57c00;
               border: 1px solid #f57c00;

               &:hover {
                 background: #fff3e0;
                 border-color: #f57c00;
                 color: #f57c00;
               }
             }
          }
        }
      }
    }
  }


}
</style>
