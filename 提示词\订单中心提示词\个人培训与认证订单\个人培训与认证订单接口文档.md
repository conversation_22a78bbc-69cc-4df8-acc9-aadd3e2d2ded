# 个人培训与认证订单API接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/order-center/personal-training`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer <PERSON>ken

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-06-20T10:00:00.000Z"
}
```

### 1.3 分页响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 2. 接口列表

### 2.1 订单管理接口

#### 2.1.1 分页查询订单列表

- **接口地址**: `POST /publicbiz/order-center/personal-training/page`
- **功能说明**: 分页查询个人培训与认证订单列表，支持多条件筛选
- **请求参数**:

| 参数名        | 类型    | 必填 | 说明                             |
| ------------- | ------- | ---- | -------------------------------- |
| page          | Integer | 是   | 当前页码，从1开始                |
| size          | Integer | 是   | 每页大小，最大100                |
| orderStatus   | String  | 否   | 订单状态筛选                     |
| paymentStatus | String  | 否   | 支付状态筛选                     |
| orderType     | String  | 否   | 订单类型筛选                     |
| keyword       | String  | 否   | 关键词搜索（学员姓名、课程名称） |
| startDate     | String  | 否   | 开始日期（YYYY-MM-DD）           |
| endDate       | String  | 否   | 结束日期（YYYY-MM-DD）           |

**请求示例**:

```json
{
  "page": 1,
  "size": 10,
  "orderStatus": "pending_payment",
  "paymentStatus": "pending",
  "orderType": "training",
  "keyword": "王小明"
}
```

**响应字段**:

| 字段名                     | 类型    | 说明              |
| -------------------------- | ------- | ----------------- |
| records                    | Array   | 订单记录列表      |
| records[].id               | Long    | 订单ID            |
| records[].orderNo          | String  | 订单号            |
| records[].studentName      | String  | 学员姓名          |
| records[].orderType        | String  | 订单类型          |
| records[].courseName       | String  | 课程/考试项目名称 |
| records[].orderSource      | String  | 订单来源          |
| records[].orderAmount      | Decimal | 订单金额          |
| records[].orderStatus      | String  | 订单状态          |
| records[].paymentStatus    | String  | 支付状态          |
| records[].learningStatus   | String  | 学习/考试状态     |
| records[].registrationTime | String  | 报名时间          |
| total                      | Integer | 总记录数          |
| size                       | Integer | 每页大小          |
| current                    | Integer | 当前页码          |
| pages                      | Integer | 总页数            |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "orderNo": "PT202406001",
        "studentName": "王小明",
        "orderType": "training",
        "courseName": "项目管理PMP认证课程",
        "orderSource": "线上小程序",
        "orderAmount": 4500.0,
        "orderStatus": "in_progress",
        "paymentStatus": "paid",
        "learningStatus": "learning",
        "registrationTime": "2024-06-10"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 2.1.2 新增订单

- **接口地址**: `POST /publicbiz/order-center/personal-training/add`
- **功能说明**: 新增个人培训与认证订单
- **请求参数**:

| 参数名              | 类型    | 必填 | 说明                                               |
| ------------------- | ------- | ---- | -------------------------------------------------- |
| businessOpportunity | String  | 否   | 关联商机ID                                         |
| associatedLead      | String  | 否   | 关联线索ID                                         |
| orderType           | String  | 是   | 订单类型：training-个人培训/certification-考试认证 |
| studentName         | String  | 是   | 学员姓名                                           |
| courseName          | String  | 是   | 课程/考试项目名称                                  |
| orderSource         | String  | 是   | 订单来源                                           |
| orderAmount         | Decimal | 是   | 订单金额                                           |
| paymentStatus       | String  | 是   | 支付状态                                           |
| collectionAmount    | Decimal | 否   | 收款金额（支付状态为已支付时必填）                 |
| collectionMethod    | String  | 否   | 收款方式（支付状态为已支付时必填）                 |
| collectionDate      | String  | 否   | 收款日期（支付状态为已支付时必填）                 |
| remark              | String  | 否   | 备注信息                                           |

**请求示例**:

```json
{
  "orderType": "training",
  "studentName": "王小明",
  "courseName": "项目管理PMP认证课程",
  "orderSource": "线上小程序",
  "orderAmount": 4500.0,
  "paymentStatus": "paid",
  "collectionAmount": 4500.0,
  "collectionMethod": "wechat",
  "collectionDate": "2024-06-20"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 1,
    "orderNo": "PT202406001"
  }
}
```

#### 2.1.3 更新订单

- **接口地址**: `POST /publicbiz/order-center/personal-training/update`
- **功能说明**: 更新个人培训与认证订单信息
- **请求参数**:

| 参数名           | 类型    | 必填 | 说明              |
| ---------------- | ------- | ---- | ----------------- |
| id               | Long    | 是   | 订单ID            |
| orderType        | String  | 否   | 订单类型          |
| studentName      | String  | 否   | 学员姓名          |
| courseName       | String  | 否   | 课程/考试项目名称 |
| orderSource      | String  | 否   | 订单来源          |
| orderAmount      | Decimal | 否   | 订单金额          |
| paymentStatus    | String  | 否   | 支付状态          |
| collectionAmount | Decimal | 否   | 收款金额          |
| collectionMethod | String  | 否   | 收款方式          |
| collectionDate   | String  | 否   | 收款日期          |
| remark           | String  | 否   | 备注信息          |

**请求示例**:

```json
{
  "id": 1,
  "studentName": "王小明",
  "orderAmount": 5000.0,
  "remark": "更新订单金额"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单更新成功",
  "data": null
}
```

#### 2.1.4 删除订单

- **接口地址**: `POST /publicbiz/order-center/personal-training/delete`
- **功能说明**: 删除个人培训与认证订单
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 订单ID |

**请求示例**:

```json
{
  "id": 1
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单删除成功",
  "data": null
}
```

#### 2.1.5 根据订单号获取订单详情

- **接口地址**: `GET /publicbiz/order-center/personal-training/getByOrderNo/{orderNo}`
- **功能说明**: 根据订单号获取订单详细信息
- **路径参数**:

| 参数名  | 类型   | 必填 | 说明   |
| ------- | ------ | ---- | ------ |
| orderNo | String | 是   | 订单号 |

**响应字段**:

| 字段名              | 类型    | 说明              |
| ------------------- | ------- | ----------------- |
| id                  | Long    | 订单ID            |
| orderNo             | String  | 订单号            |
| orderType           | String  | 订单类型          |
| businessLine        | String  | 业务线            |
| businessOpportunity | String  | 关联商机ID        |
| associatedLead      | String  | 关联线索ID        |
| studentName         | String  | 学员姓名          |
| studentPhone        | String  | 学员电话          |
| studentEmail        | String  | 学员邮箱          |
| courseName          | String  | 课程/考试项目名称 |
| courseType          | String  | 课程类型          |
| orderSource         | String  | 订单来源          |
| orderAmount         | Decimal | 订单金额          |
| orderStatus         | String  | 订单状态          |
| paymentStatus       | String  | 支付状态          |
| learningStatus      | String  | 学习状态          |
| examStatus          | String  | 考试状态          |
| registrationTime    | String  | 报名时间          |
| remark              | String  | 备注信息          |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "PT202406001",
    "orderType": "training",
    "businessLine": "个人培训与认证",
    "studentName": "王小明",
    "studentPhone": "***********",
    "courseName": "项目管理PMP认证课程",
    "orderAmount": 4500.0,
    "orderStatus": "in_progress",
    "paymentStatus": "paid",
    "learningStatus": "learning",
    "examStatus": "not_registered",
    "registrationTime": "2024-06-10"
  }
}
```

### 2.2 收款管理接口

#### 2.2.1 确认收款

- **接口地址**: `POST /publicbiz/order-center/personal-training/confirmPayment`
- **功能说明**: 确认订单收款信息
- **请求参数**:

| 参数名        | 类型    | 必填 | 说明         |
| ------------- | ------- | ---- | ------------ |
| orderId       | Long    | 是   | 订单ID       |
| paymentAmount | Decimal | 是   | 收款金额     |
| paymentType   | String  | 是   | 支付类型     |
| paymentTime   | String  | 是   | 支付时间     |
| transactionId | String  | 否   | 第三方交易号 |
| remark        | String  | 否   | 支付备注     |

**请求示例**:

```json
{
  "orderId": 1,
  "paymentAmount": 4500.0,
  "paymentType": "wechat",
  "paymentTime": "2024-06-20 10:00:00",
  "transactionId": "WX202406200001",
  "remark": "微信支付"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "收款确认成功",
  "data": {
    "paymentId": 1,
    "paymentNo": "PAY202406200001"
  }
}
```

#### 2.2.2 更新收款信息

- **接口地址**: `POST /publicbiz/order-center/personal-training/updatePayment`
- **功能说明**: 更新订单收款信息
- **请求参数**:

| 参数名        | 类型    | 必填 | 说明         |
| ------------- | ------- | ---- | ------------ |
| paymentId     | Long    | 是   | 支付记录ID   |
| paymentAmount | Decimal | 否   | 收款金额     |
| paymentType   | String  | 否   | 支付类型     |
| paymentTime   | String  | 否   | 支付时间     |
| transactionId | String  | 否   | 第三方交易号 |
| remark        | String  | 否   | 支付备注     |

**请求示例**:

```json
{
  "paymentId": 1,
  "paymentAmount": 5000.0,
  "remark": "更新收款金额"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "收款信息更新成功",
  "data": null
}
```

#### 2.2.3 获取收款详情

- **接口地址**: `GET /publicbiz/order-center/personal-training/getPaymentDetail/{orderId}`
- **功能说明**: 获取订单收款详细信息
- **路径参数**:

| 参数名  | 类型 | 必填 | 说明   |
| ------- | ---- | ---- | ------ |
| orderId | Long | 是   | 订单ID |

**响应字段**:

| 字段名        | 类型    | 说明         |
| ------------- | ------- | ------------ |
| paymentId     | Long    | 支付记录ID   |
| paymentNo     | String  | 支付单号     |
| paymentType   | String  | 支付类型     |
| paymentAmount | Decimal | 支付金额     |
| paymentStatus | String  | 支付状态     |
| paymentTime   | String  | 支付时间     |
| transactionId | String  | 第三方交易号 |
| operatorName  | String  | 操作人姓名   |
| remark        | String  | 支付备注     |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "paymentId": 1,
    "paymentNo": "PAY202406200001",
    "paymentType": "wechat",
    "paymentAmount": 4500.0,
    "paymentStatus": "success",
    "paymentTime": "2024-06-20 10:00:00",
    "transactionId": "WX202406200001",
    "operatorName": "张三",
    "remark": "微信支付"
  }
}
```

#### 2.2.4 获取收款列表

- **接口地址**: `POST /publicbiz/order-center/personal-training/getPaymentList`
- **功能说明**: 获取订单收款记录列表
- **请求参数**:

| 参数名  | 类型    | 必填 | 说明             |
| ------- | ------- | ---- | ---------------- |
| orderId | Long    | 是   | 订单ID           |
| page    | Integer | 否   | 当前页码，默认1  |
| size    | Integer | 否   | 每页大小，默认10 |

**请求示例**:

```json
{
  "orderId": 1,
  "page": 1,
  "size": 10
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "paymentId": 1,
        "paymentNo": "PAY202406200001",
        "paymentType": "wechat",
        "paymentAmount": 4500.0,
        "paymentStatus": "success",
        "paymentTime": "2024-06-20 10:00:00",
        "operatorName": "张三"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2.3 审批管理接口

#### 2.3.1 提交审批

- **接口地址**: `POST /publicbiz/order-center/personal-training/submitApproval`
- **功能说明**: 提交订单进行审批
- **请求参数**:

| 参数名        | 类型    | 必填 | 说明         |
| ------------- | ------- | ---- | ------------ |
| orderId       | Long    | 是   | 订单ID       |
| approvalType  | String  | 是   | 审批类型     |
| approvalLevel | Integer | 是   | 审批级别     |
| approverIds   | Array   | 是   | 审批人ID列表 |
| remark        | String  | 否   | 审批备注     |

**请求示例**:

```json
{
  "orderId": 1,
  "approvalType": "order_approval",
  "approvalLevel": 1,
  "approverIds": [1001, 1002],
  "remark": "请审批此订单"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "审批提交成功",
  "data": {
    "approvalId": 1,
    "approvalNo": "APV202406200001"
  }
}
```

#### 2.3.2 审批通过

- **接口地址**: `POST /publicbiz/order-center/personal-training/approve`
- **功能说明**: 审批通过订单
- **请求参数**:

| 参数名          | 类型   | 必填 | 说明                                  |
| --------------- | ------ | ---- | ------------------------------------- |
| approvalId      | Long   | 是   | 审批ID                                |
| orderId         | Long   | 是   | 订单ID                                |
| approvalResult  | String | 是   | 审批结果：approved-通过/rejected-拒绝 |
| approvalOpinion | String | 否   | 审批意见                              |
| nextApproverIds | Array  | 否   | 下一级审批人ID列表                    |

**请求示例**:

```json
{
  "approvalId": 1,
  "orderId": 1,
  "approvalResult": "approved",
  "approvalOpinion": "同意此订单",
  "nextApproverIds": [1003]
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "审批操作成功",
  "data": null
}
```

#### 2.3.3 获取审批列表

- **接口地址**: `POST /publicbiz/order-center/personal-training/getApprovalList`
- **功能说明**: 获取订单审批记录列表
- **请求参数**:

| 参数名  | 类型    | 必填 | 说明             |
| ------- | ------- | ---- | ---------------- |
| orderId | Long    | 是   | 订单ID           |
| page    | Integer | 否   | 当前页码，默认1  |
| size    | Integer | 否   | 每页大小，默认10 |

**请求示例**:

```json
{
  "orderId": 1,
  "page": 1,
  "size": 10
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "approvalId": 1,
        "approvalNo": "APV202406200001",
        "approvalType": "order_approval",
        "approvalLevel": 1,
        "approvalResult": "approved",
        "approvalOpinion": "同意此订单",
        "approverName": "张三",
        "approvalTime": "2024-06-20 10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2.4 合同管理接口

#### 2.4.1 确认合同

- **接口地址**: `POST /publicbiz/order-center/personal-training/confirmContract`
- **功能说明**: 确认订单合同信息
- **请求参数**:

| 参数名          | 类型   | 必填 | 说明                                         |
| --------------- | ------ | ---- | -------------------------------------------- |
| orderId         | Long   | 是   | 订单ID                                       |
| contractType    | String | 是   | 合同类型：electronic-电子合同/paper-纸质合同 |
| contractFileUrl | String | 否   | 合同文件URL                                  |
| contractStatus  | String | 是   | 合同状态：signed-已签署/rejected-已拒绝      |
| signer          | String | 否   | 签约人                                       |
| signDate        | String | 否   | 签约日期                                     |

**请求示例**:

```json
{
  "orderId": 1,
  "contractType": "electronic",
  "contractFileUrl": "https://example.com/contract.pdf",
  "contractStatus": "signed",
  "signer": "王小明",
  "signDate": "2024-06-20"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "合同确认成功",
  "data": null
}
```

#### 2.4.2 更新合同信息

- **接口地址**: `POST /publicbiz/order-center/personal-training/updateContract`
- **功能说明**: 更新订单合同信息
- **请求参数**:

| 参数名          | 类型   | 必填 | 说明        |
| --------------- | ------ | ---- | ----------- |
| orderId         | Long   | 是   | 订单ID      |
| contractType    | String | 否   | 合同类型    |
| contractFileUrl | String | 否   | 合同文件URL |
| contractStatus  | String | 否   | 合同状态    |
| signer          | String | 否   | 签约人      |
| signDate        | String | 否   | 签约日期    |

**请求示例**:

```json
{
  "orderId": 1,
  "contractFileUrl": "https://example.com/contract_v2.pdf",
  "signDate": "2024-06-21"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "合同信息更新成功",
  "data": null
}
```

#### 2.4.3 获取合同信息

- **接口地址**: `GET /publicbiz/order-center/personal-training/getContractInfo/{orderId}`
- **功能说明**: 获取订单合同信息
- **路径参数**:

| 参数名  | 类型 | 必填 | 说明   |
| ------- | ---- | ---- | ------ |
| orderId | Long | 是   | 订单ID |

**响应字段**:

| 字段名          | 类型   | 说明        |
| --------------- | ------ | ----------- |
| contractId      | Long   | 合同ID      |
| contractType    | String | 合同类型    |
| contractFileUrl | String | 合同文件URL |
| contractStatus  | String | 合同状态    |
| signer          | String | 签约人      |
| signDate        | String | 签约日期    |
| createTime      | String | 创建时间    |
| updateTime      | String | 更新时间    |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "contractId": 1,
    "contractType": "electronic",
    "contractFileUrl": "https://example.com/contract.pdf",
    "contractStatus": "signed",
    "signer": "王小明",
    "signDate": "2024-06-20",
    "createTime": "2024-06-20 10:00:00",
    "updateTime": "2024-06-20 10:00:00"
  }
}
```

### 2.5 操作日志接口

#### 2.5.1 获取操作日志列表

- **接口地址**: `POST /publicbiz/order-center/personal-training/getOptLogList`
- **功能说明**: 获取订单操作日志列表
- **请求参数**:

| 参数名    | 类型    | 必填 | 说明                   |
| --------- | ------- | ---- | ---------------------- |
| orderNo   | String  | 是   | 订单号                 |
| logType   | String  | 否   | 日志类型筛选           |
| startDate | String  | 否   | 开始日期（YYYY-MM-DD） |
| endDate   | String  | 否   | 结束日期（YYYY-MM-DD） |
| page      | Integer | 否   | 当前页码，默认1        |
| size      | Integer | 否   | 每页大小，默认10       |

**请求示例**:

```json
{
  "orderNo": "PT202406001",
  "logType": "订单创建",
  "startDate": "2024-06-01",
  "endDate": "2024-06-20",
  "page": 1,
  "size": 10
}
```

**响应字段**:

| 字段名                 | 类型    | 说明         |
| ---------------------- | ------- | ------------ |
| records                | Array   | 日志记录列表 |
| records[].id           | Long    | 日志ID       |
| records[].logType      | String  | 日志类型     |
| records[].logTitle     | String  | 日志标题     |
| records[].logContent   | String  | 日志内容     |
| records[].oldStatus    | String  | 原状态       |
| records[].newStatus    | String  | 新状态       |
| records[].operatorName | String  | 操作人姓名   |
| records[].operatorRole | String  | 操作人角色   |
| records[].createTime   | String  | 创建时间     |
| total                  | Integer | 总记录数     |
| size                   | Integer | 每页大小     |
| current                | Integer | 当前页码     |
| pages                  | Integer | 总页数       |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "logType": "订单创建",
        "logTitle": "创建个人培训订单",
        "logContent": "创建了订单号为PT202406001的个人培训订单",
        "oldStatus": "",
        "newStatus": "draft",
        "operatorName": "张三",
        "operatorRole": "销售员",
        "createTime": "2024-06-20 10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2.6 导出接口

#### 2.6.1 导出订单列表

- **接口地址**: `POST /publicbiz/order-center/personal-training/export`
- **功能说明**: 导出个人培训与认证订单列表到Excel文件
- **请求参数**:

| 参数名        | 类型   | 必填 | 说明            |
| ------------- | ------ | ---- | --------------- |
| orderStatus   | String | 否   | 订单状态筛选    |
| paymentStatus | String | 否   | 支付状态筛选    |
| orderType     | String | 否   | 订单类型筛选    |
| keyword       | String | 否   | 关键词搜索      |
| startDate     | String | 否   | 开始日期        |
| endDate       | String | 否   | 结束日期        |
| exportType    | String | 是   | 导出类型：excel |

**请求示例**:

```json
{
  "orderStatus": "pending_payment",
  "paymentStatus": "pending",
  "orderType": "training",
  "startDate": "2024-06-01",
  "endDate": "2024-06-20",
  "exportType": "excel"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "downloadUrl": "https://example.com/download/orders_20240620.xlsx",
    "fileName": "个人培训订单_20240620.xlsx"
  }
}
```

### 2.7 统计接口

#### 2.7.1 获取订单统计信息

- **接口地址**: `GET /publicbiz/order-center/personal-training/getStatistics`
- **功能说明**: 获取个人培训与认证订单统计信息
- **请求参数**: 无

**响应字段**:

| 字段名         | 类型    | 说明         |
| -------------- | ------- | ------------ |
| totalOrders    | Integer | 总订单数     |
| pendingOrders  | Integer | 待处理订单数 |
| monthlyAmount  | Decimal | 本月订单金额 |
| completionRate | Decimal | 订单完成率   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOrders": 18,
    "pendingOrders": 0,
    "monthlyAmount": 2135220.0,
    "completionRate": 92.5
  }
}
```

## 3. 数据字典

### 3.1 订单类型

| 值            | 说明     |
| ------------- | -------- |
| training      | 个人培训 |
| certification | 考试认证 |

### 3.2 订单状态

| 值               | 说明   |
| ---------------- | ------ |
| draft            | 草稿   |
| pending_approval | 待审批 |
| approving        | 审批中 |
| approved         | 已批准 |
| rejected         | 已拒绝 |
| pending_payment  | 待支付 |
| executing        | 执行中 |
| completed        | 已完成 |
| cancelled        | 已取消 |

### 3.3 支付状态

| 值        | 说明   |
| --------- | ------ |
| pending   | 待支付 |
| paid      | 已支付 |
| refunded  | 已退款 |
| cancelled | 已取消 |

### 3.4 学习状态

| 值          | 说明   |
| ----------- | ------ |
| not_started | 未开始 |
| learning    | 学习中 |
| completed   | 已完成 |

### 3.5 考试状态

| 值             | 说明   |
| -------------- | ------ |
| not_registered | 未报名 |
| registered     | 已报名 |
| passed         | 已通过 |
| failed         | 未通过 |

### 3.6 合同状态

| 值       | 说明   |
| -------- | ------ |
| unsigned | 未签署 |
| signed   | 已签署 |
| rejected | 已拒绝 |

### 3.7 支付类型

| 值            | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 3.8 合同类型

| 值         | 说明     |
| ---------- | -------- |
| electronic | 电子合同 |
| paper      | 纸质合同 |

### 3.9 订单来源

| 值                    | 说明       |
| --------------------- | ---------- |
| online_miniprogram    | 线上小程序 |
| offline_registration  | 线下报名   |
| phone_consultation    | 电话咨询   |
| friend_recommendation | 朋友推荐   |

### 3.10 支付方式

| 值            | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 3.11 学习状态

| 值          | 说明   |
| ----------- | ------ |
| not_started | 未开始 |
| learning    | 学习中 |
| completed   | 已完成 |

### 3.12 考试状态

| 值             | 说明   |
| -------------- | ------ |
| not_registered | 未报名 |
| registered     | 已报名 |
| passed         | 已通过 |
| failed         | 未通过 |

### 3.13 日志类型

| 值         | 说明         |
| ---------- | ------------ |
| 订单创建   | 订单创建操作 |
| 订单编辑   | 订单编辑操作 |
| 审批通过   | 审批通过操作 |
| 审批驳回   | 审批驳回操作 |
| 确认收款   | 确认收款操作 |
| 订单完成   | 订单完成操作 |
| 系统管理员 | 系统自动操作 |

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明           | 解决方案                 |
| ------ | -------------- | ------------------------ |
| 200    | 请求成功       | -                        |
| 400    | 请求参数错误   | 检查请求参数格式和必填项 |
| 401    | 未授权         | 检查用户登录状态和权限   |
| 403    | 禁止访问       | 检查用户是否有操作权限   |
| 404    | 资源不存在     | 检查请求的资源ID是否正确 |
| 500    | 服务器内部错误 | 联系系统管理员           |

### 4.2 业务错误码

| 错误码                  | 说明               | 解决方案                 |
| ----------------------- | ------------------ | ------------------------ |
| ORDER_NOT_FOUND         | 订单不存在         | 检查订单号是否正确       |
| ORDER_STATUS_INVALID    | 订单状态不允许操作 | 检查订单当前状态         |
| PAYMENT_AMOUNT_INVALID  | 支付金额无效       | 检查支付金额是否大于0    |
| STUDENT_INFO_INCOMPLETE | 学员信息不完整     | 完善学员基本信息         |
| COURSE_NOT_AVAILABLE    | 课程不可用         | 检查课程是否已下架或暂停 |
| DUPLICATE_ORDER         | 重复订单           | 检查是否已存在相同订单   |
| INSUFFICIENT_PERMISSION | 权限不足           | 联系管理员分配相应权限   |

## 5. 接口调用示例

### 5.1 创建个人培训订单

```javascript
// 请求示例
const response = await fetch('/order/personal/add', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    orderType: 'training',
    studentName: '张三',
    studentPhone: '***********',
    studentEmail: '<EMAIL>',
    courseName: 'Python编程基础课程',
    courseDescription: 'Python编程入门课程',
    courseDuration: '30课时',
    courseFee: 2800.00,
    orderSource: 'online_miniprogram',
    businessOpportunity: 'OPP202406001',
    associatedLead: 'LEAD202406001'
  })
});

// 响应示例
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 12345,
    "orderNo": "PT202406001",
    "createTime": "2024-06-01 10:00:00"
  }
}
```

### 5.2 查询订单列表

```javascript
// 请求示例
const response = await fetch('/order/personal/page?page=1&size=10&orderStatus=pending_payment', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

// 响应示例
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 12345,
        "orderNo": "PT202406001",
        "studentName": "张三",
        "orderType": "training",
        "courseName": "Python编程基础课程",
        "orderAmount": 2800.00,
        "orderStatus": "pending_payment",
        "paymentStatus": "pending",
        "learningStatus": "not_started",
        "createTime": "2024-06-01 10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

### 5.3 确认收款

```javascript
// 请求示例
const response = await fetch('/order/personal/confirmPayment', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    orderId: 12345,
    paymentAmount: 2800.00,
    paymentType: 'wechat',
    paymentTime: '2024-06-01 15:30:00',
    paymentRemark: '微信支付'
  })
});

// 响应示例
{
  "code": 200,
  "message": "收款确认成功",
  "data": {
    "paymentId": 67890,
    "paymentNo": "PAY202406001",
    "updateTime": "2024-06-01 15:30:00"
  }
}
```

## 6. 注意事项

### 6.1 接口调用规范

1. 所有接口都需要在请求头中携带有效的认证token
2. 请求参数中的金额字段必须为数字类型，精确到分
3. 日期时间字段格式统一为：YYYY-MM-DD HH:mm:ss
4. 分页查询的页码从1开始，每页大小建议不超过100

### 6.2 业务规则

1. 订单创建后默认状态为"草稿"，需要手动提交审批
2. 只有已支付的订单才能进入学习状态
3. 考试认证类订单需要先完成学习才能报名考试
4. 订单状态变更会自动记录操作日志
5. 删除订单为逻辑删除，不会物理删除数据

### 6.3 性能优化

1. 列表查询支持多字段组合搜索，建议合理使用索引
2. 批量操作接口支持最多100条记录同时处理
3. 导出功能建议异步处理，避免长时间等待

### 6.4 安全考虑

1. 敏感信息（如身份证号）在传输和存储时需要进行加密
2. 操作日志会记录所有关键操作，便于审计追踪
3. 权限控制基于用户角色和部门进行细粒度管理

---

**文档版本**: v1.0  
**最后更新**: 2024-06-01  
**维护人员**: 系统开发团队  
**联系方式**: <EMAIL>
